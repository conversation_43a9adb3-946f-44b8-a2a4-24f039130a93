import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import confusion_matrix
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
import dill
import time

# 特征数变量
num_features = 7
window_size = 1000  # 窗口大小
step_size = 1000  # 滑动步长

# 自定义数据集类
class ValveDataset(Dataset):
    def __init__(self, data, labels):
        self.data = data
        self.labels = labels

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        sample = self.data[idx]
        label = self.labels[idx]
        return sample, label

# 滑动窗口函数
def sliding_window(data, labels, window_size, step_size):
    windows = []
    window_labels = []
    for i in range(0, len(data) - window_size + 1, step_size):
        window = data[i:i + window_size]
        windows.append(window)
        window_labels.append(labels[i])
    return np.array(windows), np.array(window_labels)

# 对数据进行傅里叶变换
def fourier_transform(data):
    transformed_data = np.fft.fft(data, axis=0)
    return np.abs(transformed_data)

# 读取数据文件
data_normal = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\正常.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault1 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\堵塞80.5.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault2 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\堵塞92.8.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault3 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏0.23.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault4 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏0.58.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault5 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏1.74.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values

# # 对数据进行傅里叶变换
# data_normal = fourier_transform(data_normal)
# data_fault1 = fourier_transform(data_fault1)
# data_fault2 = fourier_transform(data_fault2)
# data_fault3 = fourier_transform(data_fault3)
# data_fault4 = fourier_transform(data_fault4)
# data_fault5 = fourier_transform(data_fault5)

# 滑动窗口处理每个文件的数据，并打标签
windows_normal, labels_normal = sliding_window(data_normal, np.zeros(len(data_normal)), window_size, step_size)
windows_fault1, labels_fault1 = sliding_window(data_fault1, np.ones(len(data_fault1)), window_size, step_size)
windows_fault2, labels_fault2 = sliding_window(data_fault2, np.full(len(data_fault2), 2), window_size, step_size)
windows_fault3, labels_fault3 = sliding_window(data_fault3, np.full(len(data_fault3), 3), window_size, step_size)
windows_fault4, labels_fault4 = sliding_window(data_fault4, np.full(len(data_fault4), 4), window_size, step_size)
windows_fault5, labels_fault5 = sliding_window(data_fault5, np.full(len(data_fault5), 5), window_size, step_size)

# 合并数据和标签
windows = np.concatenate((windows_normal, windows_fault1, windows_fault2, windows_fault3, windows_fault4, windows_fault5))
window_labels = np.concatenate((labels_normal, labels_fault1, labels_fault2, labels_fault3, labels_fault4, labels_fault5))

# 将数据和标签转换为 Tensor 对象
data_tensor = torch.tensor(windows, dtype=torch.float32)
labels_tensor = torch.tensor(window_labels, dtype=torch.long)

# 创建自定义数据集对象
dataset = ValveDataset(data_tensor, labels_tensor)

# 划分训练集和测试集
train_size = int(0.7 * len(dataset))
test_size = len(dataset) - train_size
train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

# 创建批次加载器
batch_size = 256
train_dataloader = DataLoader(train_dataset, shuffle=True, batch_size=batch_size)
test_dataloader = DataLoader(test_dataset, shuffle=False, batch_size=batch_size)

# 创建卷积神经网络模型
class CNN(nn.Module):
    def __init__(self, num_features, window_size):
        super(CNN, self).__init__()

        self.net = nn.Sequential(
            nn.Conv1d(num_features, 16, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(16, 32, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(32, 64, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(64, 128, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Dropout(p=0.5)
        )

        flattened_size = self.compute_flattened_size(window_size)

        self.fc = nn.Sequential(
            nn.Linear(flattened_size, 84),
            nn.ReLU(),
            nn.Dropout(p=0.5),
            nn.Linear(84, 6)
        )

    def compute_flattened_size(self, sequence_length):
        with torch.no_grad():
            x = torch.zeros(1, num_features, sequence_length)
            x = self.net(x)
            return x.numel()

    def forward(self, x):
        x = self.net(x)
        x = x.view(x.size(0), -1)
        y = self.fc(x)
        return y

# 检查并设置GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Device:", device)

# 实例化模型、损失函数和优化器
model = CNN(num_features, window_size).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 模型训练
num_epochs = 100
best_accuracy = 0.0
losses = []
accuracy_list = []
start_time = time.time()

for epoch in range(num_epochs):
    epoch_start_time = time.time()
    model.train()
    for batch_data, batch_labels in train_dataloader:
        batch_data = batch_data.to(device)
        batch_labels = batch_labels.to(device)
        batch_data = batch_data.permute(0, 2, 1)

        # 前向传播
        outputs = model(batch_data)
        loss = criterion(outputs, batch_labels)
        losses.append(loss.item())

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    # 计算测试集的准确率
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for batch_data, batch_labels in test_dataloader:
            batch_data = batch_data.to(device)
            batch_labels = batch_labels.to(device)
            batch_data = batch_data.permute(0, 2, 1)
            # print(batch_data.shape)
            outputs = model(batch_data)
            _, predicted = torch.max(outputs.data, 1)
            total += batch_labels.size(0)
            correct += (predicted == batch_labels).sum().item()

    accuracy = 100 * correct / total
    accuracy_list.append(accuracy)
    print(f"Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item()}, Test Accuracy: {accuracy}%")

    # 保存最佳模型
    if accuracy > best_accuracy:
        best_accuracy = accuracy
        torch.save(model.state_dict(), 'best_model.pth')
        with open('best_model.pkl', 'wb') as f:
            dill.dump(model, f)

    # epoch_end_time = time.time()
    # epoch_time = epoch_end_time - epoch_start_time
    # total_elapsed_time = epoch_end_time - start_time
    # print(f"Time for epoch {epoch + 1}: {epoch_time:.2f} seconds, Total elapsed time: {total_elapsed_time:.2f} seconds")

total_training_time = time.time() - start_time
print(f"Total training time: {total_training_time:.2f} seconds")

# 绘制损失函数和准确率变化图
fig, axs = plt.subplots(2, figsize=(12, 10))

axs[0].plot(range(len(losses)), losses)
axs[0].set_title('Loss over epochs')
axs[0].set_xlabel('Iteration')
axs[0].set_ylabel('Loss')

axs[1].plot(range(1, len(accuracy_list) + 1), accuracy_list)
axs[1].set_title('Test Accuracy over epochs')
axs[1].set_xlabel('Epoch')
axs[1].set_ylabel('Accuracy (%)')

plt.show()

# 加载最佳模型
best_model = CNN(num_features, window_size).to(device)
best_model.load_state_dict(torch.load('best_model.pth'))

# 创建一个空的真实标签列表和预测标签列表
true_labels = []
predicted_labels = []

# 模型测试（使用测试集）
best_model.eval()
with torch.no_grad():
    for batch_data, batch_labels in test_dataloader:
        batch_data = batch_data.to(device)
        batch_labels = batch_labels.to(device)
        batch_data = batch_data.permute(0, 2, 1)
        outputs = best_model(batch_data)
        _, predicted = torch.max(outputs.data, 1)
        true_labels.extend(batch_labels.cpu().numpy())
        predicted_labels.extend(predicted.cpu().numpy())

# 计算混淆矩阵
conf_matrix = confusion_matrix(true_labels, predicted_labels)
print("Confusion Matrix:")
print(conf_matrix)

best_model.cpu()  # 转移到 CPU 设备
# ✅ 使用 tracing 方式导出 TorchScript（推荐用于 C++ 调用）
example_input = torch.randn(71, 7, 1000) # ⚠️ 你模型实际输入 shape
traced_script_module = torch.jit.trace(best_model, example_input)

# ✅ 保存为兼容 C++ 的 TorchScript 文件
traced_script_module.save("CNN1_ts.pt")  # 文件名后缀区分原来的 CNN1.pt
print("模型成功导出为 CNN1_ts.pt（TorchScript 格式）")
print(any(param.is_cuda for param in traced_script_module.parameters()))
# 输出应为 False，表示模型为 CPU 模型



