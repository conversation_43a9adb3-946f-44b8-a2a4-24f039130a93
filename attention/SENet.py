import torch
import torch.nn as nn
import math

# SENet的实现
class se_block(nn.Module):
    def __init__(self, channel, ratio=16):
        super(se_block, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
                nn.Linear(channel, channel // ratio, bias=False),
                nn.ReLU(inplace=True),
                nn.Linear(channel // ratio, channel, bias=False),
                nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y

# import torch
# import torch.nn as nn
# import math
#
# class se_block(nn.Module):
#     def __init__(self, channel, ratio=16):
#         super(se_block, self).__init__()
#         self.avg_pool = nn.AdaptiveAvgPool2d(1)
#         self.fc = nn.Sequential(
#                 nn.Linear(channel, channel // ratio, bias=False),
#                 nn.ReLU(inplace=True),
#                 nn.Linear(channel // ratio, channel, bias=False),
#                 nn.Sigmoid()
#         )
#
#     def forward(self, x):
#         b, c, _, _ = x.size()
#         y = self.avg_pool(x).view(b, c)
#         y = self.fc(y).view(b, c, 1, 1)
#         return x * y
#
# model= se_block(512)
# print(model)
# inputs = torch.ones([512, 26, 26])
# outputs = model(inputs)

