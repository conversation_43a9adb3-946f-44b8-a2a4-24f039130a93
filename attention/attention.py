import torch
import torch.nn as nn


class Attention(nn.Module):
    def __init__(self, in_planes, out_planes, kernel_size=1, stride=1):
        super(Attention, self).__init__()
        self.conv_q = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.conv_k = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.conv_v = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.softmax = nn.Softmax(dim=-1)
        self.gamma = nn.Parameter(torch.zeros(1))  # 可学习的标量参数

    def forward(self, x):
        q = self.conv_q(x)
        k = self.conv_k(x)
        v = self.conv_v(x)

        b, c, h, w = q.size()

        q = q.view(b, c, -1).permute(0, 2, 1)  # (b, h*w, c)
        k = k.view(b, c, -1)  # (b, c, h*w)
        v = v.view(b, c, -1).permute(0, 2, 1)  # (b, h*w, c)

        attn = torch.bmm(q, k)  # (b, h*w, h*w)
        attn = self.softmax(attn)

        out = torch.bmm(attn, v)  # (b, h*w, c)
        out = out.permute(0, 2, 1).contiguous().view(b, c, h, w)  # (b, c, h, w)

        out = self.gamma * out + x  # 残差连接

        return out


# 使用示例
in_planes = 3
out_planes = 256
kernel_size = 3

x = torch.randn(1, in_planes, 64, 64)
attention = Attention(in_planes, out_planes, kernel_size=kernel_size)
output = attention(x)
print(output.shape)  # 输出: torch.Size([1, 256, 64, 64])
