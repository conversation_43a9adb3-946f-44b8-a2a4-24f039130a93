import torch
import torch.nn as nn


class ACmix2(nn.Module):
    def __init__(self, in_planes, out_planes, kernel_size=1, stride=1):
        super(ACmix2, self).__init__()
        self.conv_q = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.conv_k = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.conv_v = nn.Conv2d(in_planes, out_planes, kernel_size=kernel_size)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x):
        q = self.conv_q(x)
        k = self.conv_k(x)
        v = self.conv_v(x)

        b, c, h, w = q.size()

        q = q.view(b, c, -1)  # (b, c, h*w)
        k = k.view(b, c, -1)  # (b, c, h*w)
        v = v.view(b, c, -1)  # (b, c, h*w)

        attn = torch.bmm(q.transpose(1, 2), k)  # (b, h*w, h*w)
        attn = self.softmax(attn)

        out = torch.bmm(v, attn.transpose(1, 2))  # (b, c, h*w)
        out = out.view(b, c, h, w)  # (b, c, h, w)

        return out
# 这部分代码实现了经典的自注意力机制（Self-Attention）计算过程

# 使用示例
in_planes = 3
out_planes = 256
kernel_size = 3

x = torch.randn(1, in_planes, 64, 64)
attention = ACmix2(in_planes, out_planes, kernel_size=kernel_size)
output = attention(x)
print(output.shape)  # 输出: torch.Size([1, 256, 64, 64])
