import torch
import numpy as np
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split # 划分训练集和测试集
import matplotlib.pyplot as plt
import seaborn as sns

from attention.EMA import EMA  # 调用注意力机制

# 检查并设置GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print("Device:", device)

# 读取每个类别的数据文件，存储为列表或NumPy数组
# 数据预处理：首先，将每个类别的数据整理成一个完整的数据集。对于每个类别的数据文件，读取文件并将其存储为一个列表或NumPy数组
data_25magnet_1s = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_25magnet_1s.csv", delimiter=",")
data_normal = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_normal.csv", delimiter=",")
data_openfault = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_openfault.csv", delimiter=",")
data_shortfault = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_shortfault.csv", delimiter=",")
data_stuckdead = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_stuckdead.csv", delimiter=",")
data_TXfault_1 = np.loadtxt("D:\PyCharm\cnn\阀故障数据\致命故障_测试数据\致命故障_测试数据\data_TXfault_1.csv", delimiter=",")

# 创建标签数组
# 创建标签：为了实现分类任务，需要为每个数据点创建相应的标签。对于每个类别的数据集，创建一个与数据集长度相同的标签数组,例如data_25magnet_1s数据集有30046行数据，创建30046个标签
labels_25magnet_1s = np.full(len(data_25magnet_1s), 0)
labels_normal = np.full(len(data_normal), 1)
labels_openfault = np.full(len(data_openfault), 2)
labels_shortfault = np.full(len(data_shortfault), 3)
labels_stuckdead = np.full(len(data_stuckdead), 4)
labels_TXfault_1 = np.full(len(data_TXfault_1), 5)

# 合并数据和标签
# 合并数据和标签：将所有类别的数据和标签合并成单个数据集和标签数组
data = np.concatenate((data_25magnet_1s, data_normal, data_openfault, data_shortfault, data_stuckdead, data_TXfault_1))
labels = np.concatenate((labels_25magnet_1s, labels_normal, labels_openfault, labels_shortfault, labels_stuckdead, labels_TXfault_1))

# 创建卷积神经网络模型
class CNN(nn.Module):
    def __init__(self):
        super(CNN, self).__init__()

        self.net = nn.Sequential(
            nn.Conv2d(1, 6, kernel_size=(1, 10)),
            nn.Tanh(),
            nn.AvgPool2d(kernel_size=1, stride=1),
            nn.Conv2d(6, 16, kernel_size=1),
            nn.Tanh(),
            nn.AvgPool2d(kernel_size=1, stride=1),
            nn.Conv2d(16, 120, kernel_size=1),
            nn.Tanh()
        )
        # 添加注意力机制模块
        self.EMA = EMA(channels=120)

        self.fc = nn.Sequential(
            nn.Linear(120, 84),
            nn.Tanh(),
            nn.Linear(84, 120)
        )

    def forward(self, x):
        x = self.net(x)
        x = self.EMA(x)  # 应用注意力机制模块对特征图进行加权处理
        x = x.view(x.size(0), -1)
        y = self.fc(x)
        return y

# 查看网络结构
X = torch.rand(size= (256, 1, 1, 10))
for layer in CNN().net:
    X = layer(X)
    print( layer.__class__.__name__, 'output shape: \t', X.shape )

# 将数据和标签转换为 Tensor 对象
data_tensor = torch.Tensor(data)
labels_tensor = torch.Tensor(labels).long()

# 划分训练集和测试集
train_data, test_data, train_labels, test_labels = train_test_split(data_tensor, labels_tensor, test_size=0.2, random_state=42)

# 创建训练集和测试集的 TensorDataset 对象
train_dataset = TensorDataset(train_data, train_labels)
test_dataset = TensorDataset(test_data, test_labels)

# 创建批次加载器
batch_size = 256
train_dataloader = DataLoader(train_dataset, shuffle=True, batch_size=batch_size)
test_dataloader = DataLoader(test_dataset, shuffle=False, batch_size=batch_size)

# 实例化模型、损失函数和优化器
model = CNN().to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 模型训练
num_epochs = 10
losses = []  # 记录损失函数变化的列表

for epoch in range(num_epochs):
    for batch_data, batch_labels in train_dataloader:
        # 把数据和标签小批量读取到GPU上
        batch_data = batch_data.to(device)
        batch_labels = batch_labels.to(device)
        # 在输入数据上进行适当的预处理，以满足卷积神经网络的要求
        # 假设原始数据是二维图像数据，需要将其转换为四维张量 [batch_size, channels, height, width]
        batch_data = batch_data.unsqueeze(1).unsqueeze(1)

        # 前向传播
        print(batch_data.shape)

        outputs = model(batch_data)
        loss = criterion(outputs, batch_labels)
        losses.append(loss.item())  # 记录损失函数的变化

        # 反向传播和优化
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    # 输出当前轮次的损失
    print(f"Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item()}")

Fig = plt.figure()
plt.plot(range(len(losses)), losses)
plt.show()  # 输出损失函数图像

# 创建一个空的真实标签列表和预测标签列表
true_labels = []
predicted_labels = []

# 模型测试（使用测试集）
correct = 0
total = 0
with torch.no_grad():
    for batch_data, batch_labels in test_dataloader:
        # 把数据和标签小读取到GPU上
        batch_data = batch_data.to(device)
        batch_labels = batch_labels.to(device)

        batch_data = batch_data.unsqueeze(1).unsqueeze(1)
        outputs = model(batch_data)
        _, predicted = torch.max(outputs.data, 1)
        predicted = predicted.to(batch_labels)
        total += batch_labels.size(0)
        correct += (predicted == batch_labels).sum().item()

        # 添加真实标签和预测标签到列表中
        true_labels.extend(batch_labels.cpu().numpy())
        predicted_labels.extend(predicted.cpu().numpy())

# 计算混淆矩阵
from sklearn.metrics import confusion_matrix
conf_matrix = confusion_matrix(true_labels, predicted_labels)
print("Confusion Matrix:")
print(conf_matrix)

# 计算准确率
accuracy = 100 * correct / total
print(f"Test Accuracy: {accuracy}%")

# 保存模型
torch.save(model, 'CNN1.pth')


scripted = torch.jit.script(model)
scripted.save("CNN1.pt")




