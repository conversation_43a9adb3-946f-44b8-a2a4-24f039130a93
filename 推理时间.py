import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.metrics import confusion_matrix
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
import dill
import time

# 特征数变量
num_features = 7
window_size = 1000  # 窗口大小
step_size = 1000  # 滑动步长

# 自定义数据集类
class ValveDataset(Dataset):
    def __init__(self, data, labels):
        self.data = data
        self.labels = labels

    def __len__(self):
        return len(self.labels)

    def __getitem__(self, idx):
        sample = self.data[idx]
        label = self.labels[idx]
        return sample, label

# 滑动窗口函数
def sliding_window(data, labels, window_size, step_size):
    windows = []
    window_labels = []
    for i in range(0, len(data) - window_size + 1, step_size):
        window = data[i:i + window_size]
        windows.append(window)
        window_labels.append(labels[i])
    return np.array(windows), np.array(window_labels)

# 对数据进行傅里叶变换
def fourier_transform(data):
    transformed_data = np.fft.fft(data, axis=0)
    return np.abs(transformed_data)

# 读取数据文件
data_normal = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\正常.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault1 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\堵塞80.5.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault2 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\堵塞92.8.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault3 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏0.23.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault4 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏0.58.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values
data_fault5 = pd.read_csv(r"D:\PyCharm\ZaoQiWeiRuo\EHA实验数据\早期微弱故障\csv数据_20mm02hz\泄漏1.74.csv", delimiter=",", header=None, skiprows=1, encoding='utf-8').values

# # 对数据进行傅里叶变换
# data_normal = fourier_transform(data_normal)
# data_fault1 = fourier_transform(data_fault1)
# data_fault2 = fourier_transform(data_fault2)
# data_fault3 = fourier_transform(data_fault3)
# data_fault4 = fourier_transform(data_fault4)
# data_fault5 = fourier_transform(data_fault5)

# 滑动窗口处理每个文件的数据，并打标签
windows_normal, labels_normal = sliding_window(data_normal, np.zeros(len(data_normal)), window_size, step_size)
windows_fault1, labels_fault1 = sliding_window(data_fault1, np.ones(len(data_fault1)), window_size, step_size)
windows_fault2, labels_fault2 = sliding_window(data_fault2, np.full(len(data_fault2), 2), window_size, step_size)
windows_fault3, labels_fault3 = sliding_window(data_fault3, np.full(len(data_fault3), 3), window_size, step_size)
windows_fault4, labels_fault4 = sliding_window(data_fault4, np.full(len(data_fault4), 4), window_size, step_size)
windows_fault5, labels_fault5 = sliding_window(data_fault5, np.full(len(data_fault5), 5), window_size, step_size)

# 合并数据和标签
windows = np.concatenate((windows_normal, windows_fault1, windows_fault2, windows_fault3, windows_fault4, windows_fault5))
window_labels = np.concatenate((labels_normal, labels_fault1, labels_fault2, labels_fault3, labels_fault4, labels_fault5))

# 将数据和标签转换为 Tensor 对象
data_tensor = torch.tensor(windows, dtype=torch.float32)
labels_tensor = torch.tensor(window_labels, dtype=torch.long)

# 创建自定义数据集对象
dataset = ValveDataset(data_tensor, labels_tensor)

# 划分训练集和测试集
train_size = int(0.7 * len(dataset))
test_size = len(dataset) - train_size
train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

# 创建批次加载器
batch_size = 256
train_dataloader = DataLoader(train_dataset, shuffle=True, batch_size=batch_size)
test_dataloader = DataLoader(test_dataset, shuffle=False, batch_size=batch_size)

# 创建卷积神经网络模型
class CNN(nn.Module):
    def __init__(self, num_features, window_size):
        super(CNN, self).__init__()

        self.net = nn.Sequential(
            nn.Conv1d(num_features, 16, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(16, 32, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(32, 64, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Conv1d(64, 128, kernel_size=4),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            nn.Dropout(p=0.5)
        )

        flattened_size = self.compute_flattened_size(window_size)

        self.fc = nn.Sequential(
            nn.Linear(flattened_size, 84),
            nn.ReLU(),
            nn.Dropout(p=0.5),
            nn.Linear(84, 6)
        )

    def compute_flattened_size(self, sequence_length):
        with torch.no_grad():
            x = torch.zeros(1, num_features, sequence_length)
            x = self.net(x)
            return x.numel()

    def forward(self, x):
        x = self.net(x)
        x = x.view(x.size(0), -1)
        y = self.fc(x)
        return y

# 检查并设置GPU
device = torch.device("cpu" if torch.cuda.is_available() else "cpu")
print("Device:", device)

# 实例化模型、损失函数和优化器
model = CNN(num_features, window_size).to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 模型训练
num_epochs = 100
best_accuracy = 0.0
losses = []
accuracy_list = []
start_time = time.time()


total_training_time = time.time() - start_time
print(f"Total training time: {total_training_time:.2f} seconds")

# 绘制损失函数和准确率变化图
fig, axs = plt.subplots(2, figsize=(12, 10))

axs[0].plot(range(len(losses)), losses)
axs[0].set_title('Loss over epochs')
axs[0].set_xlabel('Iteration')
axs[0].set_ylabel('Loss')

axs[1].plot(range(1, len(accuracy_list) + 1), accuracy_list)
axs[1].set_title('Test Accuracy over epochs')
axs[1].set_xlabel('Epoch')
axs[1].set_ylabel('Accuracy (%)')

plt.show()

# 加载最佳模型
best_model = CNN(num_features, window_size).to(device)
best_model.load_state_dict(torch.load('best_model.pth'))

# 创建一个空的真实标签列表和预测标签列表
true_labels = []
predicted_labels = []

import time

# 模型测试（使用测试集）
best_model.eval()
inference_times = []  # 存储每次batch的推理时间（单位：秒）
true_labels = []  # 存储真实标签
predicted_labels = []  # 存储预测标签

with torch.no_grad():
    for batch_data, batch_labels in test_dataloader:
        # 数据预处理
        batch_data = batch_data.to(device)
        batch_labels = batch_labels.to(device)
        batch_data = batch_data.permute(0, 2, 1)  # 调整维度顺序

        # 计时开始
        start_time = time.time()

        # 模型推理
        outputs = best_model(batch_data)

        # 计时结束
        end_time = time.time()

        # 记录本次batch的总推理时间
        batch_time = end_time - start_time
        inference_times.append(batch_time)

        # 计算预测结果
        _, predicted = torch.max(outputs.data, 1)

        # 收集标签
        true_labels.extend(batch_labels.cpu().numpy())
        predicted_labels.extend(predicted.cpu().numpy())

        # 打印当前batch中单个样本的平均推理时间（毫秒）
        avg_sample_time_ms = (batch_time / batch_size) * 1000
        print(f"当前batch单个样本平均推理时间: {avg_sample_time_ms:.4f} ms")

# 计算整体统计信息
total_samples = len(true_labels)
total_time = sum(inference_times)
avg_time_per_sample_ms = (total_time / total_samples) * 1000

print("\n===== 推理时间统计 =====")
print(f"总测试样本数: {total_samples}")
print(f"总推理时间: {total_time:.4f} 秒")
print(f"单个样本平均推理时间: {avg_time_per_sample_ms:.4f} ms")
print(f"FPS (样本/秒): {total_samples / total_time:.2f}")






# ===== 新增：随机生成单个样本测试 =====
print("\n===== 开始单个样本测试 =====")

# 获取输入特征的维度信息（假设从第一个batch获取）
sample_shape = batch_data[0].shape  # 获取(C, L)形状
print(f"输入样本形状: {sample_shape}")

# 生成随机测试样本（模拟真实输入）
random_sample = torch.randn(1, *sample_shape).to(device)  # 添加batch维度

# 进行10次推理测试（消除第一次的初始化影响）
single_sample_times = []
for i in range(10):
    start_time = time.time()
    output = best_model(random_sample)
    end_time = time.time()
    elapsed = (end_time - start_time) * 1000  # 转换为毫秒
    single_sample_times.append(elapsed)

    # 打印每次结果（第一次可能较慢）
    _, pred = torch.max(output.data, 1)
    print(f"测试 {i + 1}: 推理时间 = {elapsed:.4f} ms, 预测类别 = {pred.item()}")

# 计算统计信息（忽略第一次的warm-up）
avg_single_time = np.mean(single_sample_times[1:])
std_single_time = np.std(single_sample_times[1:])
min_single_time = np.min(single_sample_times[1:])
max_single_time = np.max(single_sample_times[1:])

print("\n===== 单个样本时间统计 =====")
print(f"平均推理时间: {avg_single_time:.4f} ms")
print(f"标准差: {std_single_time:.4f} ms")
print(f"最小时间: {min_single_time:.4f} ms")
print(f"最大时间: {max_single_time:.4f} ms")
print(f"FPS: {1000 / avg_single_time:.2f} (样本/秒)")


