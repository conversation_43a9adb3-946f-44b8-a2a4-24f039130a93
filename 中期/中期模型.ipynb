#%%
from numpy import random
import pandas as pd
import torch
import numpy as np
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns

from attention.ECA import eca_block  # 调用注意力机制

def set_random_seed(seed=42):
    """设置随机种子以确保训练过程的可重复性"""

    # 设置 Python 内部随机数生成器的种子
    random.seed(seed)

    # 设置 Numpy 的随机数种子
    np.random.seed(seed)

    # 设置 PyTorch 的随机数种子
    torch.manual_seed(seed)

    # 如果使用 GPU，还需要设置 CUDA 随机种子
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)



# 设置随机种子
set_random_seed(42)

class CNN1D(nn.Module):
    def __init__(self, input_length, num_classes=8, feature_dim=64):
        super(CNN1D, self).__init__()

        # 1. 各通道独立特征提取
        self.channel_nets = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(1, 16, kernel_size=3, padding=1),
                nn.BatchNorm1d(16),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2, stride=2),

                nn.Conv1d(16, 32, kernel_size=3, padding=1),
                nn.BatchNorm1d(32),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2, stride=2),

                nn.Conv1d(32, feature_dim, kernel_size=3, padding=1),
                nn.BatchNorm1d(feature_dim),
                nn.ReLU(),
                nn.AdaptiveAvgPool1d(1)
            ) for _ in range(7)
        ])

        # 2. 手动计算融合后的特征维度
        # 计算经过两个MaxPool后的序列长度
        seq_len = input_length // 4  # 因为做了两次/2的池化
        self.fused_dim = feature_dim * 7  # 7个通道拼接

        # 3. 跨通道融合网络
        self.fusion_net = nn.Sequential(
            nn.Conv1d(self.fused_dim, 256, kernel_size=3, padding=1),
            # eca_block(self.fused_dim),  # 使用ECA注意力机制
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1)
        )

        # 4. 分类器
        self.classifier = nn.Sequential(
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, num_classes)
        )

    def _forward_features(self, x):
        """返回各通道特征和拼接特征"""
        channel_features = []
        channel_outputs = []

        for i in range(7):
            # [B, 1, L] -> [B, feature_dim, 1]
            feat = self.channel_nets[i](x[:, i:i + 1])
            channel_outputs.append(feat.squeeze(-1))

            # 将特征扩展回原始长度比例 [B, feature_dim, L//4]
            expanded_feat = feat.expand(-1, -1, x.shape[2] // 4)
            channel_features.append(expanded_feat)

        return torch.cat(channel_features, dim=1), channel_outputs

    def forward(self, x):
        # 特征提取
        fused_features, channel_features = self._forward_features(x)
        # print(fused_features.shape)
        # print(channel_features[0].shape)  # 每个通道的特征形状
        # 特征融合
        fused = self.fusion_net(fused_features).squeeze(-1)

        # 分类
        logits = self.classifier(fused)

        # 调整通道特征维度
        # torch.stack(channel_features, dim=1)  # [B, 7, feature_dim]
        channel_features = torch.stack(channel_features, dim=2)  # [B, feature_dim, 7]

        return  logits, channel_features


if __name__ == "__main__":
    # 检查并设置GPU
    # device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # print("Device:", device)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print("Device:", device)

    # 读取每个类别的数据文件，并进行标准化
    def load_and_standardize_data(file_path):
        data = pd.read_csv(file_path, skiprows=1, header=None, encoding='utf-8').values
        scaler = StandardScaler()
        return scaler.fit_transform(data)

    # 将每个类别的数据标准化
    data_0 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\正常.csv")
    data_1 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\堵塞80.5.csv")
    data_2 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\堵塞92.8.csv")
    data_3 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\泄漏0.23.csv")
    data_4 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\泄漏0.58.csv")
    data_5 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\泄漏1.74.csv")
    data_6 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\混合0.58_92.8.csv")
    data_7 = load_and_standardize_data("D:\\PyCharm\\ZaoQiWeiRuo\\EHA实验数据\\早期微弱故障\\csv数据_20mm02hz\\混合1.74_92.8.csv")

    # 滑动窗口生成样本
    def create_sliding_windows(data, window_size, step_size=1):
        num_windows = (len(data) - window_size) // step_size + 1
        windows = np.array([data[i:i + window_size] for i in range(0, num_windows * step_size, step_size)])
        return windows

    window_size = 500  # 滑动窗口大小
    step_size = 500

    # 应用滑动窗口
    data_0_windows = create_sliding_windows(data_0, window_size, step_size)
    data_1_windows = create_sliding_windows(data_1, window_size, step_size)
    data_2_windows = create_sliding_windows(data_2, window_size, step_size)
    data_3_windows = create_sliding_windows(data_3, window_size, step_size)
    data_4_windows = create_sliding_windows(data_4, window_size, step_size)
    data_5_windows = create_sliding_windows(data_5, window_size, step_size)
    data_6_windows = create_sliding_windows(data_6, window_size, step_size)
    data_7_windows = create_sliding_windows(data_7, window_size, step_size)

    # 创建标签
    labels_0 = np.full(len(data_0_windows), 0)
    labels_1 = np.full(len(data_1_windows), 1)
    labels_2 = np.full(len(data_2_windows), 2)
    labels_3 = np.full(len(data_3_windows), 3)
    labels_4 = np.full(len(data_4_windows), 4)
    labels_5 = np.full(len(data_5_windows), 5)
    labels_6 = np.full(len(data_6_windows), 6)
    labels_7 = np.full(len(data_7_windows), 7)

    # 合并数据和标签
    data = np.concatenate((data_0_windows, data_1_windows, data_2_windows, data_3_windows, data_4_windows, data_5_windows, data_6_windows, data_7_windows))
    labels = np.concatenate((labels_0, labels_1, labels_2, labels_3, labels_4, labels_5, labels_6, labels_7))



    # 将数据和标签转换为 Tensor 对象
    data_tensor = torch.Tensor(data).permute(0, 2, 1)  # 调整维度顺序为 (batch_size, num_features, sequence_length)
    labels_tensor = torch.Tensor(labels).long()

    # 划分训练集和测试集
    train_data, test_data, train_labels, test_labels = train_test_split(data_tensor, labels_tensor, test_size=0.2, random_state=42)

    # 创建训练集和测试集的 TensorDataset 对象
    train_dataset = TensorDataset(train_data, train_labels)
    test_dataset = TensorDataset(test_data, test_labels)

    # 创建批次加载器
    batch_size = 256
    train_dataloader = DataLoader(train_dataset, shuffle=True, batch_size=batch_size)
    test_dataloader = DataLoader(test_dataset, shuffle=False, batch_size=batch_size)

    # 实例化模型、损失函数和优化器
    # model = CNN1D(input_channels=7, input_length=window_size).to(device)
    model = CNN1D(input_length=window_size).to(device)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.0001)


    # 模型训练
    num_epochs = 500
    losses = []  # 记录损失函数变化的列表
    train_accuracies = []  # 记录训练准确率的列表
    test_accuracies = []  # 记录测试准确率的列表

    for epoch in range(num_epochs):
        model.train()
        train_correct = 0
        train_total = 0
        for batch_data, batch_labels in train_dataloader:
            # 把数据和标签小批量读取到GPU上
            batch_data = batch_data.to(device)
            batch_labels = batch_labels.to(device)

            # 前向传播
            # print(batch_data.shape)
            outputs, _ = model(batch_data)
            loss = criterion(outputs, batch_labels)
            losses.append(loss.item())  # 记录损失函数的变化

            # 计算训练准确率
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_labels.size(0)
            train_correct += (predicted == batch_labels).sum().item()

            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        train_accuracy = 100 * train_correct / train_total
        train_accuracies.append(train_accuracy)

        # 计算测试准确率
        model.eval()
        test_correct = 0
        test_total = 0

        with torch.no_grad():
            for batch_data, batch_labels in test_dataloader:
                # 把数据和标签小批量读取到GPU上
                batch_data = batch_data.to(device)
                batch_labels = batch_labels.to(device)

                outputs, _ = model(batch_data)
                _, predicted = torch.max(outputs.data, 1)
                test_total += batch_labels.size(0)
                test_correct += (predicted == batch_labels).sum().item()

        test_accuracy = 100 * test_correct / test_total
        test_accuracies.append(test_accuracy)

        # 输出当前轮次的损失和准确率
        print(f"Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item():.4f}, Train Accuracy: {train_accuracy:.2f}%, Test Accuracy: {test_accuracy:.2f}%")

        # 提前停止条件（可选）
        if len(test_accuracies) > 5 and all(x >= 99 for x in test_accuracies[-5:]):
            print("Early stopping as test accuracy reached 99% for 5 consecutive epochs.")
            break

    # 绘制损失函数变化图
    plt.figure()
    plt.plot(range(len(losses)), losses)
    plt.xlabel('Iteration')
    plt.ylabel('Loss')
    plt.title('Training Loss')
    plt.show()

    # 绘制准确率曲线
    plt.figure()
    plt.plot(range(len(train_accuracies)), train_accuracies, label='Train Accuracy')
    plt.plot(range(len(test_accuracies)), test_accuracies, label='Test Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Test Accuracy')
    plt.legend()
    plt.show()

    # 绘制混淆矩阵
    model.eval()
    all_predicted = []
    all_labels = []
    with torch.no_grad():
        for batch_data, batch_labels in test_dataloader:
            # 把数据和标签小批量读取到GPU上
            batch_data = batch_data.to(device)
            batch_labels = batch_labels.to(device)

            outputs, _ = model(batch_data)
            _, predicted = torch.max(outputs.data, 1)
            all_predicted.extend(predicted.cpu().numpy())
            all_labels.extend(batch_labels.cpu().numpy())

    conf_matrix = confusion_matrix(all_labels, all_predicted)
    plt.figure(figsize=(10, 7))
    sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues')
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.show()


    torch.save(model.state_dict(), 'cnn1d.pth')



#%%

#%%
from sklearn.manifold import TSNE

# 合并数据和标签
data = np.concatenate((data_0_windows, data_1_windows, data_2_windows, data_3_windows, data_4_windows, data_5_windows, data_6_windows, data_7_windows))
labels = np.concatenate((labels_0, labels_1, labels_2, labels_3, labels_4, labels_5, labels_6, labels_7))

# 将数据和标签转换为 Tensor 对象
data_tensor = torch.Tensor(data).permute(0, 2, 1)  # 调整维度顺序为 (batch_size, num_features, sequence_length)
labels_tensor = torch.Tensor(labels).long()

dataset = TensorDataset(data_tensor, labels_tensor)

batch_size = 256
dataloader = DataLoader(dataset, shuffle=True, batch_size=batch_size)


# t-SNE 可视化（降维并绘制聚类）
def plot_tsne(model, data_loader):
    model.eval()
    all_predicted, all_labels = [], []

    with torch.no_grad():
        for batch_data, batch_labels in dataloader:
            # 把数据和标签小批量读取到GPU上
            batch_data = batch_data.to(device)
            batch_labels = batch_labels.to(device)

            outputs, _ = model(batch_data)
            _, predicted = torch.max(outputs.data, 1)
            all_predicted.extend(outputs.cpu().numpy())
            all_labels.extend(batch_labels.cpu().numpy())

    features = np.array(all_predicted)
    labels = np.array(all_labels)

    tsne = TSNE(n_components=2, random_state=42)
    tsne_result = tsne.fit_transform(features)

    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(tsne_result[:, 0], tsne_result[:, 1], c=labels, cmap="viridis", alpha=0.7)
    plt.colorbar(scatter, label="Class")
    plt.title("t-SNE Visualization of Feature Clusters")
    plt.show()

# 绘制 t-SNE 聚类图
plot_tsne(model, dataloader)