#
# import serial
# import struct
# import time
# import threading
# import numpy as np
# import matplotlib.pyplot as plt
# from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
# import tkinter as tk
# from tkinter import ttk, messagebox, filedialog
# import queue
# import serial.tools.list_ports
# import sys
# import locale
# import os
#
# # 导入协议解析器和管理器
# from protocol_parser import ProtocolParser
# from protocol_manager import ProtocolEditor, ProtocolManager
#
# # --- 基本配置 ---
# if sys.platform.startswith('win'):
#     try:
#         locale.setlocale(locale.LC_ALL, 'chs')
#     except:
#         pass
#
# plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
# plt.rcParams['axes.unicode_minus'] = False
#
# DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"
# BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
# FRAME_HEADER = b'\xaa\x55'
#
# # --- 全局协议解析器实例 ---
# protocol_parser = ProtocolParser()
# if os.path.exists(DEFAULT_PROTOCOL_FILE):
#     protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
# else:
#     print(f"错误: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'。")
#
#
# # --- 主接收逻辑类 ---
# class Protocol422Receiver:
#     def __init__(self, port=None, baudrate=460800, timeout=0.1):
#         self.port = port
#         self.baudrate = baudrate
#         self.timeout = timeout
#         self.ser = None
#         self.buffer = bytearray()
#         self.data_queue = queue.Queue(maxsize=200)
#         self.running = False
#         self.max_history = 200
#         self.data_history = {}
#         self.update_protocol_structure()
#
#     def update_protocol_structure(self):
#         """当协议文件重载时，更新内部结构"""
#         self.protocol_structure = protocol_parser.get_protocol_structure()
#         self.frame_length = self._calculate_frame_length()
#         self._init_data_history()
#         print(f"接收端协议已更新。帧长度: {self.frame_length}字节。")
#
#     def _calculate_frame_length(self):
#         if not self.protocol_structure: return 0
#         return self.protocol_structure[-1]['byte_indices'][-1] + 1
#
#     def _init_data_history(self):
#         self.data_history.clear()
#         for field in self.protocol_structure:
#             if field['name'] not in ['帧头', '校验和']:
#                 self.data_history[field['name']] = []
#
#     def connect(self, port, baudrate):
#         try:
#             if self.ser and self.ser.is_open: self.ser.close()
#             self.port, self.baudrate = port, baudrate
#             self.ser = serial.Serial(port=port, baudrate=baudrate, timeout=self.timeout)
#             self.buffer.clear()
#             return True, f"已连接到 {port}, 波特率 {baudrate}"
#         except serial.SerialException as e:
#             return False, f"串口连接失败: {e}"
#
#     def start(self):
#         if self.running: return False, "已在运行"
#         if not self.ser or not self.ser.is_open: return False, "串口未连接"
#         self.running = True
#         self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
#         self.receive_thread.start()
#         return True, "开始接收数据"
#
#     def stop(self):
#         self.running = False
#         if hasattr(self, 'receive_thread'): self.receive_thread.join(timeout=1.0)
#         if self.ser and self.ser.is_open: self.ser.close()
#         return True, "已停止接收"
#
#     def _receive_loop(self):
#         while self.running:
#             try:
#                 if self.ser.in_waiting > 0:
#                     self.buffer.extend(self.ser.read(self.ser.in_waiting))
#                     self._process_buffer()
#                 else:
#                     time.sleep(0.001)
#             except Exception as e:
#                 print(f"接收循环错误: {e}")
#                 self.stop()
#
#     def _process_buffer(self):
#         while len(self.buffer) >= self.frame_length:
#             frame_start = self.buffer.find(FRAME_HEADER)
#             if frame_start == -1:
#                 self.buffer = self.buffer[-(len(FRAME_HEADER) - 1):]
#                 break
#             if frame_start > 0:
#                 self.buffer = self.buffer[frame_start:]
#             if len(self.buffer) < self.frame_length: break
#
#             raw_frame = self.buffer[:self.frame_length]
#             checksum_field = protocol_parser.get_field_by_name('校验和')
#             is_valid = False
#             if not checksum_field:
#                 is_valid = True
#             else:
#                 checksum_idx = checksum_field['byte_indices'][0]
#                 received_checksum = raw_frame[checksum_idx]
#                 calculated_checksum = 0
#                 for i in range(2, checksum_idx):
#                     calculated_checksum ^= raw_frame[i]
#                 is_valid = (calculated_checksum == received_checksum)
#
#             if is_valid:
#                 parsed_data = self._parse_frame(raw_frame)
#                 self._update_data_history(parsed_data)
#                 if not self.data_queue.full():
#                     self.data_queue.put({'raw': raw_frame, 'parsed': parsed_data})
#                 self.buffer = self.buffer[self.frame_length:]
#             else:
#                 self.buffer = self.buffer[1:]
#
#     def _parse_frame(self, raw_frame):
#         parsed_data = {}
#         for field in self.protocol_structure:
#             name, start_idx, data_type = field['name'], field['byte_indices'][0], field['type']
#             try:
#                 if data_type == 'int16':
#                     value = struct.unpack_from('<h', raw_frame, start_idx)[0]
#                 elif data_type == 'uint16':
#                     value = struct.unpack_from('<H', raw_frame, start_idx)[0]
#                 elif data_type == 'int8':
#                     value = struct.unpack_from('<b', raw_frame, start_idx)[0]
#                 elif data_type == 'uint8':
#                     value = struct.unpack_from('<B', raw_frame, start_idx)[0]
#                 else:
#                     value = raw_frame[start_idx]
#                 parsed_data[name] = value
#             except struct.error as e:
#                 parsed_data[name] = "Error"
#         return parsed_data
#
#     def _update_data_history(self, parsed_data):
#         for name, value in parsed_data.items():
#             if name in self.data_history and isinstance(value, (int, float)):
#                 self.data_history[name].append(value)
#                 if len(self.data_history[name]) > self.max_history:
#                     self.data_history[name].pop(0)
#
#     def get_latest_data(self):
#         try:
#             return self.data_queue.get_nowait()
#         except queue.Empty:
#             return None
#
#
# # --- GUI部分 ---
# class ReceiverGUI:
#     def __init__(self, master):
#         self.master = master
#         self.master.title("422通讯接收端")
#         self.master.geometry("1300x800")
#
#         self.receiver = Protocol422Receiver()
#         self.status_var = tk.StringVar(value="就绪")
#         self.data_vars = {}
#         self.colors = plt.cm.get_cmap('tab20', 20)
#
#         self.create_widgets()
#         self.update_port_list()
#         self.master.after(100, self.update_gui)
#
#     def create_widgets(self):
#         menubar = tk.Menu(self.master)
#         self.master.config(menu=menubar)
#         file_menu = tk.Menu(menubar, tearoff=0)
#         menubar.add_cascade(label="文件", menu=file_menu)
#
#         # !! 新增：协议编辑器菜单项 !!
#         file_menu.add_command(label="协议编辑器", command=self.open_protocol_editor)
#         file_menu.add_separator()
#         file_menu.add_command(label="退出", command=self.on_closing)
#
#         main_frame = ttk.Frame(self.master, padding=5)
#         main_frame.pack(fill=tk.BOTH, expand=True)
#
#         control_frame = ttk.Frame(main_frame)
#         control_frame.pack(fill=tk.X, pady=(0, 5))
#
#         ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=2)
#         self.port_combobox = ttk.Combobox(control_frame, width=10)
#         self.port_combobox.pack(side=tk.LEFT, padx=2)
#         ttk.Label(control_frame, text="波特率:").pack(side=tk.LEFT, padx=2)
#         self.baudrate_combobox = ttk.Combobox(control_frame, values=[str(b) for b in BAUDRATES], width=10)
#         self.baudrate_combobox.pack(side=tk.LEFT, padx=2)
#         self.baudrate_combobox.set("460800")
#         ttk.Button(control_frame, text="刷新", command=self.update_port_list).pack(side=tk.LEFT, padx=2)
#
#         self.start_button = ttk.Button(control_frame, text="启动接收", command=self.start_receiver)
#         self.start_button.pack(side=tk.LEFT, padx=(10, 2))
#         self.stop_button = ttk.Button(control_frame, text="停止接收", command=self.stop_receiver, state=tk.DISABLED)
#         self.stop_button.pack(side=tk.LEFT, padx=2)
#         self.clear_button = ttk.Button(control_frame, text="清除数据", command=self.clear_data)
#         self.clear_button.pack(side=tk.LEFT, padx=2)
#
#         paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
#         paned_window.pack(fill=tk.BOTH, expand=True)
#
#         left_panel = ttk.Frame(paned_window, width=250)
#         paned_window.add(left_panel, weight=1)
#         right_panel = ttk.Frame(paned_window, width=800)
#         paned_window.add(right_panel, weight=4)
#
#         select_frame = ttk.LabelFrame(left_panel, text="数据显示选择")
#         select_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
#
#         select_canvas = tk.Canvas(select_frame)
#         scrollbar = ttk.Scrollbar(select_frame, orient="vertical", command=select_canvas.yview)
#         self.checkbox_frame = ttk.Frame(select_canvas)
#         self.checkbox_frame.bind("<Configure>",
#                                  lambda e: select_canvas.configure(scrollregion=select_canvas.bbox("all")))
#         select_canvas.create_window((0, 0), window=self.checkbox_frame, anchor="nw")
#         select_canvas.configure(yscrollcommand=scrollbar.set)
#         select_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
#         scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
#
#         table_frame = ttk.LabelFrame(left_panel, text="当前帧数据")
#         table_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
#
#         columns = ("name", "value", "hex")
#         self.data_table = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
#         self.data_table.heading("name", text="字段");
#         self.data_table.column("name", width=120)
#         self.data_table.heading("value", text="数值");
#         self.data_table.column("value", width=60)
#         self.data_table.heading("hex", text="十六进制");
#         self.data_table.column("hex", width=80)
#         self.data_table.pack(fill=tk.BOTH, expand=True)
#
#         self.fig, self.ax = plt.subplots();
#         self.fig.tight_layout()
#         self.canvas = FigureCanvasTkAgg(self.fig, master=right_panel)
#         self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
#
#         status_bar = ttk.Frame(self.master, relief=tk.SUNKEN, padding="2 5")
#         status_bar.pack(side=tk.BOTTOM, fill=tk.X)
#         ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT)
#         self.protocol_file_var = tk.StringVar(
#             value=os.path.basename(DEFAULT_PROTOCOL_FILE) if os.path.exists(DEFAULT_PROTOCOL_FILE) else "未加载")
#         ttk.Label(status_bar, textvariable=self.protocol_file_var).pack(side=tk.RIGHT)
#         ttk.Label(status_bar, text="当前协议: ").pack(side=tk.RIGHT)
#
#         self.rebuild_ui_from_protocol()
#
#     def on_protocol_updated(self):
#         """协议编辑器保存后的回调函数"""
#         print("主程序接收到协议更新通知。")
#         self.receiver.update_protocol_structure()
#         self.rebuild_ui_from_protocol()
#         # 更新显示的文件名
#         pm = ProtocolManager()
#         self.protocol_file_var.set(os.path.basename(pm.get_current_protocol_file() or "未命名"))
#         messagebox.showinfo("协议更新", "接收端已应用新的协议，数据已清除。")
#
#     def open_protocol_editor(self):
#         """打开协议编辑器窗口"""
#         if self.receiver.running:
#             messagebox.showwarning("提示", "请先停止接收再编辑协议。")
#             return
#
#         editor_window = tk.Toplevel(self.master)
#         editor_window.title("协议编辑器")
#         editor_window.geometry("800x600")
#         ProtocolEditor(editor_window, protocol_parser, on_save_callback=self.on_protocol_updated)
#         editor_window.transient(self.master)
#         editor_window.grab_set()
#
#     def rebuild_ui_from_protocol(self):
#         """根据当前协议完全重建UI相关部分"""
#         # 重建复选框
#         for widget in self.checkbox_frame.winfo_children(): widget.destroy()
#         self.data_vars.clear()
#
#         btn_frame = ttk.Frame(self.checkbox_frame)
#         btn_frame.pack(fill=tk.X)
#         ttk.Button(btn_frame, text="全选", command=lambda: self.toggle_all_checkboxes(True)).pack(side=tk.LEFT, padx=2,
#                                                                                                   pady=2)
#         ttk.Button(btn_frame, text="全不选", command=lambda: self.toggle_all_checkboxes(False)).pack(side=tk.LEFT,
#                                                                                                      padx=2, pady=2)
#
#         for field in self.receiver.protocol_structure:
#             name = field['name']
#             if name in ['帧头', '校验和']: continue
#             var = tk.BooleanVar(value=False)
#             cb = ttk.Checkbutton(self.checkbox_frame, text=name, variable=var)
#             cb.pack(anchor=tk.W, padx=5)
#             self.data_vars[name] = var
#
#         # 清除数据和图表
#         self.clear_data()
#
#     def toggle_all_checkboxes(self, state):
#         for var in self.data_vars.values(): var.set(state)
#
#     def update_port_list(self):
#         ports = [port.device for port in serial.tools.list_ports.comports()]
#         self.port_combobox['values'] = ports if ports else ["无可用串口"]
#         if ports: self.port_combobox.current(0)
#
#     def start_receiver(self):
#         port = self.port_combobox.get()
#         if not port or "无可用串口" in port:
#             messagebox.showerror("错误", "请选择有效串口。")
#             return
#
#         success, message = self.receiver.connect(port, int(self.baudrate_combobox.get()))
#         if not success:
#             messagebox.showerror("连接错误", message)
#             return
#
#         self.receiver.start()
#         self.status_var.set("正在接收...")
#         self.start_button.config(state=tk.DISABLED)
#         self.stop_button.config(state=tk.NORMAL)
#
#     def stop_receiver(self):
#         self.receiver.stop()
#         self.status_var.set("已停止")
#         self.start_button.config(state=tk.NORMAL)
#         self.stop_button.config(state=tk.DISABLED)
#
#     def clear_data(self):
#         self.receiver._init_data_history()
#         self.data_table.delete(*self.data_table.get_children())
#         self.update_plot()
#         self.status_var.set("数据已清除")
#
#     def update_gui(self):
#         latest_data = self.receiver.get_latest_data()
#         if latest_data: self.update_table(latest_data['parsed'])
#         self.update_plot()
#         self.master.after(100, self.update_gui)
#
#     def update_table(self, parsed_data):
#         self.data_table.delete(*self.data_table.get_children())
#         for name, value in parsed_data.items():
#             try:
#                 hex_val = f"{int(value):X}"
#             except:
#                 hex_val = "N/A"
#             self.data_table.insert("", tk.END, values=(name, value, hex_val))
#
#     def update_plot(self):
#         self.ax.clear()
#         has_plot = False
#         for i, (name, var) in enumerate(self.data_vars.items()):
#             if var.get():
#                 data = self.receiver.data_history.get(name, [])
#                 if data:
#                     self.ax.plot(data, label=name, color=self.colors(i % 20))
#                     has_plot = True
#
#         self.ax.grid(True)
#         self.ax.set_title("实时数据");
#         self.ax.set_xlabel("采样点");
#         self.ax.set_ylabel("数值")
#         if has_plot: self.ax.legend(loc='upper left', fontsize='small')
#         self.fig.tight_layout()
#         self.canvas.draw()
#
#     def on_closing(self):
#         if messagebox.askokcancel("退出", "确定要退出吗?"):
#             self.receiver.stop()
#             self.master.destroy()
#
#
# def main():
#     root = tk.Tk()
#     app = ReceiverGUI(root)
#     root.protocol("WM_DELETE_WINDOW", app.on_closing)
#     root.mainloop()
#
#
# if __name__ == "__main__":
#     main()

import serial
import struct
import time
import threading
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import queue
import serial.tools.list_ports
import sys
import locale
import os

# 导入协议解析器和管理器
from protocol_parser import ProtocolParser
from protocol_manager import ProtocolEditor, ProtocolManager

# --- 基本配置 ---
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
FRAME_HEADER = b'\xaa\x55'

# --- 全局协议解析器实例 ---
protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
else:
    print(f"错误: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'。")


# --- 主接收逻辑类 (此类代码无需修改) ---
class Protocol422Receiver:
    def __init__(self, port=None, baudrate=460800, timeout=0.1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        self.buffer = bytearray()
        self.data_queue = queue.Queue(maxsize=200)
        self.running = False
        self.max_history = 200
        self.data_history = {}
        self.update_protocol_structure()

    def update_protocol_structure(self):
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.frame_length = self._calculate_frame_length()
        self._init_data_history()
        print(f"接收端协议已更新。帧长度: {self.frame_length}字节。")

    def _calculate_frame_length(self):
        if not self.protocol_structure: return 0
        return self.protocol_structure[-1]['byte_indices'][-1] + 1

    def _init_data_history(self):
        self.data_history.clear()
        for field in self.protocol_structure:
            if field['name'] not in ['帧头', '校验和']:
                self.data_history[field['name']] = []

    def connect(self, port, baudrate):
        try:
            if self.ser and self.ser.is_open: self.ser.close()
            self.port, self.baudrate = port, baudrate
            self.ser = serial.Serial(port=port, baudrate=baudrate, timeout=self.timeout)
            self.buffer.clear()
            return True, f"已连接到 {port}, 波特率 {baudrate}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"

    def start(self):
        if self.running: return False, "已在运行"
        if not self.ser or not self.ser.is_open: return False, "串口未连接"
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
        self.receive_thread.start()
        return True, "开始接收数据"

    def stop(self):
        self.running = False
        if hasattr(self, 'receive_thread'): self.receive_thread.join(timeout=1.0)
        if self.ser and self.ser.is_open: self.ser.close()
        return True, "已停止接收"

    def _receive_loop(self):
        while self.running:
            try:
                if self.ser.in_waiting > 0:
                    self.buffer.extend(self.ser.read(self.ser.in_waiting))
                    self._process_buffer()
                else:
                    time.sleep(0.001)
            except Exception as e:
                print(f"接收循环错误: {e}")
                self.stop()

    def _process_buffer(self):
        while len(self.buffer) >= self.frame_length:
            frame_start = self.buffer.find(FRAME_HEADER)
            if frame_start == -1:
                self.buffer = self.buffer[-(len(FRAME_HEADER) - 1):]
                break
            if frame_start > 0: self.buffer = self.buffer[frame_start:]
            if len(self.buffer) < self.frame_length: break
            raw_frame = self.buffer[:self.frame_length]
            checksum_field = protocol_parser.get_field_by_name('校验和')
            is_valid = False
            if not checksum_field:
                is_valid = True
            else:
                checksum_idx = checksum_field['byte_indices'][0]
                received_checksum = raw_frame[checksum_idx]
                calculated_checksum = 0
                for i in range(2, checksum_idx): calculated_checksum ^= raw_frame[i]
                is_valid = (calculated_checksum == received_checksum)
            if is_valid:
                parsed_data = self._parse_frame(raw_frame)
                self._update_data_history(parsed_data)
                if not self.data_queue.full():
                    self.data_queue.put({'raw': raw_frame, 'parsed': parsed_data})
                self.buffer = self.buffer[self.frame_length:]
            else:
                self.buffer = self.buffer[1:]

    def _parse_frame(self, raw_frame):
        parsed_data = {}
        for field in self.protocol_structure:
            name, start_idx, data_type = field['name'], field['byte_indices'][0], field['type']
            try:
                if data_type == 'int16':
                    value = struct.unpack_from('<h', raw_frame, start_idx)[0]
                elif data_type == 'uint16':
                    value = struct.unpack_from('<H', raw_frame, start_idx)[0]
                elif data_type == 'int8':
                    value = struct.unpack_from('<b', raw_frame, start_idx)[0]
                elif data_type == 'uint8':
                    value = struct.unpack_from('<B', raw_frame, start_idx)[0]
                else:
                    value = raw_frame[start_idx]
                parsed_data[name] = value
            except struct.error:
                parsed_data[name] = "Error"
        return parsed_data

    def _update_data_history(self, parsed_data):
        for name, value in parsed_data.items():
            if name in self.data_history and isinstance(value, (int, float)):
                self.data_history[name].append(value)
                if len(self.data_history[name]) > self.max_history:
                    self.data_history[name].pop(0)

    def get_latest_data(self):
        try:
            return self.data_queue.get_nowait()
        except queue.Empty:
            return None


# --- GUI部分 (已修复图例警告) ---
class ReceiverGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯接收端")
        self.master.geometry("1300x800")
        self.receiver = Protocol422Receiver()
        self.status_var = tk.StringVar(value="就绪")
        self.data_vars = {}
        self.colors = plt.cm.get_cmap('tab20', 20)
        self.display_mode_var = tk.StringVar(value="合并视图")
        self.plot_widgets = {}

        self.create_widgets()
        self.update_port_list()
        self.master.after(100, self.update_loop)

    def create_widgets(self):
        menubar = tk.Menu(self.master);
        self.master.config(menu=menubar);
        file_menu = tk.Menu(menubar, tearoff=0);
        menubar.add_cascade(label="文件", menu=file_menu);
        file_menu.add_command(label="协议编辑器", command=self.open_protocol_editor);
        file_menu.add_separator();
        file_menu.add_command(label="退出", command=self.on_closing)
        main_frame = ttk.Frame(self.master, padding=5);
        main_frame.pack(fill=tk.BOTH, expand=True);
        control_frame = ttk.Frame(main_frame);
        control_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=2);
        self.port_combobox = ttk.Combobox(control_frame, width=10);
        self.port_combobox.pack(side=tk.LEFT, padx=2);
        ttk.Label(control_frame, text="波特率:").pack(side=tk.LEFT, padx=2);
        self.baudrate_combobox = ttk.Combobox(control_frame, values=[str(b) for b in BAUDRATES], width=10);
        self.baudrate_combobox.pack(side=tk.LEFT, padx=2);
        self.baudrate_combobox.set("460800");
        ttk.Button(control_frame, text="刷新", command=self.update_port_list).pack(side=tk.LEFT, padx=2);
        self.start_button = ttk.Button(control_frame, text="启动接收", command=self.start_receiver);
        self.start_button.pack(side=tk.LEFT, padx=(10, 2));
        self.stop_button = ttk.Button(control_frame, text="停止接收", command=self.stop_receiver, state=tk.DISABLED);
        self.stop_button.pack(side=tk.LEFT, padx=2);
        self.clear_button = ttk.Button(control_frame, text="清除数据", command=self.clear_data);
        self.clear_button.pack(side=tk.LEFT, padx=2)
        ttk.Label(control_frame, text="显示模式:").pack(side=tk.LEFT, padx=(10, 2));
        self.display_mode_combo = ttk.Combobox(control_frame, textvariable=self.display_mode_var,
                                               values=["合并视图", "独立视图"], width=10);
        self.display_mode_combo.pack(side=tk.LEFT)
        self.display_mode_combo.bind("<<ComboboxSelected>>", lambda e: self._rebuild_plot_area())
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL);
        paned_window.pack(fill=tk.BOTH, expand=True);
        left_panel = ttk.Frame(paned_window, width=300);
        paned_window.add(left_panel, weight=1);
        right_panel = ttk.Frame(paned_window);
        paned_window.add(right_panel, weight=4)
        select_frame = ttk.LabelFrame(left_panel, text="数据显示选择");
        select_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2);
        select_canvas = tk.Canvas(select_frame);
        scrollbar = ttk.Scrollbar(select_frame, orient="vertical", command=select_canvas.yview);
        self.checkbox_frame = ttk.Frame(select_canvas);
        self.checkbox_frame.bind("<Configure>",
                                 lambda e: select_canvas.configure(scrollregion=select_canvas.bbox("all")));
        select_canvas.create_window((0, 0), window=self.checkbox_frame, anchor="nw");
        select_canvas.configure(yscrollcommand=scrollbar.set);
        select_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True);
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        table_frame = ttk.LabelFrame(left_panel, text="当前帧数据");
        table_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2);
        columns = ("name", "value", "hex");
        self.data_table = ttk.Treeview(table_frame, columns=columns, show="headings", height=10);
        self.data_table.heading("name", text="字段");
        self.data_table.column("name", width=120);
        self.data_table.heading("value", text="数值");
        self.data_table.column("value", width=60);
        self.data_table.heading("hex", text="十六进制");
        self.data_table.column("hex", width=80);
        self.data_table.pack(fill=tk.BOTH, expand=True)
        self.plot_canvas = tk.Canvas(right_panel);
        plot_scrollbar = ttk.Scrollbar(right_panel, orient="vertical", command=self.plot_canvas.yview);
        self.plot_scroll_frame = ttk.Frame(self.plot_canvas);
        self.plot_scroll_frame.bind("<Configure>",
                                    lambda e: self.plot_canvas.configure(scrollregion=self.plot_canvas.bbox("all")));
        self.plot_canvas.create_window((0, 0), window=self.plot_scroll_frame, anchor="nw");
        self.plot_canvas.configure(yscrollcommand=plot_scrollbar.set);
        self.plot_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True);
        plot_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        status_bar = ttk.Frame(self.master, relief=tk.SUNKEN, padding="2 5");
        status_bar.pack(side=tk.BOTTOM, fill=tk.X);
        ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT);
        self.protocol_file_var = tk.StringVar(
            value=os.path.basename(DEFAULT_PROTOCOL_FILE) if os.path.exists(DEFAULT_PROTOCOL_FILE) else "未加载");
        ttk.Label(status_bar, textvariable=self.protocol_file_var).pack(side=tk.RIGHT);
        ttk.Label(status_bar, text="当前协议: ").pack(side=tk.RIGHT)

        self.rebuild_ui_from_protocol()

    def rebuild_ui_from_protocol(self):
        for widget in self.checkbox_frame.winfo_children(): widget.destroy()
        self.data_vars.clear()
        btn_frame = ttk.Frame(self.checkbox_frame)
        btn_frame.pack(fill=tk.X)
        ttk.Button(btn_frame, text="全选", command=lambda: self.toggle_all_checkboxes(True)).pack(side=tk.LEFT, padx=2,
                                                                                                  pady=2)
        ttk.Button(btn_frame, text="全不选", command=lambda: self.toggle_all_checkboxes(False)).pack(side=tk.LEFT,
                                                                                                     padx=2, pady=2)
        for field in self.receiver.protocol_structure:
            name = field['name']
            if name in ['帧头', '校验和']: continue
            var = tk.BooleanVar(value=False)
            cb = ttk.Checkbutton(self.checkbox_frame, text=name, variable=var, command=self._rebuild_plot_area)
            cb.pack(anchor=tk.W, padx=5)
            self.data_vars[name] = var
        self.clear_data()

    def _rebuild_plot_area(self):
        for widget in self.plot_scroll_frame.winfo_children(): widget.destroy()
        self.plot_widgets.clear()
        selected_fields = [name for name, var in self.data_vars.items() if var.get()]
        if not selected_fields: return
        mode = self.display_mode_var.get()
        if mode == "合并视图":
            fig, ax = plt.subplots(figsize=(8, 6), dpi=100)
            canvas = FigureCanvasTkAgg(fig, master=self.plot_scroll_frame)
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            self.plot_widgets['combined'] = {'fig': fig, 'ax': ax, 'canvas': canvas, 'fields': selected_fields}
        elif mode == "独立视图":
            for name in selected_fields:
                fig, ax = plt.subplots(figsize=(8, 2.5), dpi=100)
                canvas = FigureCanvasTkAgg(fig, master=self.plot_scroll_frame)
                canvas.get_tk_widget().pack(fill=tk.X, expand=True, padx=5, pady=2)
                self.plot_widgets[name] = {'fig': fig, 'ax': ax, 'canvas': canvas}
        self._update_plot_data()

    def _update_plot_data(self):
        mode = self.display_mode_var.get()
        if mode == "合并视图":
            if 'combined' in self.plot_widgets:
                widgets = self.plot_widgets['combined']
                ax, canvas = widgets['ax'], widgets['canvas']
                ax.clear()

                # !! 修复：增加一个标志位来判断是否真的画了线 !!
                has_plotted_anything = False

                for i, name in enumerate(widgets['fields']):
                    data = self.receiver.data_history.get(name, [])
                    if data:
                        ax.plot(data, label=name, color=self.colors(i % 20))
                        has_plotted_anything = True  # 如果画了，设置标志位

                ax.grid(True);
                ax.set_title("合并视图");
                ax.set_xlabel("采样点");
                ax.set_ylabel("数值")

                # !! 修复：只有在真的画了线的情况下才显示图例 !!
                if has_plotted_anything:
                    ax.legend(loc='upper left', fontsize='small')

                canvas.draw()

        elif mode == "独立视图":
            for name, widgets in self.plot_widgets.items():
                ax, canvas, fig = widgets['ax'], widgets['canvas'], widgets['fig']
                ax.clear()
                data = self.receiver.data_history.get(name, [])
                if data:
                    ax.plot(data, color=self.colors(list(self.plot_widgets.keys()).index(name) % 20))
                ax.grid(True);
                ax.set_title(name);
                ax.set_xlabel("采样点");
                ax.set_ylabel("数值")
                fig.tight_layout()
                canvas.draw()

    def update_loop(self):
        latest_data = self.receiver.get_latest_data()
        if latest_data:
            self.update_table(latest_data['parsed'])
        self._update_plot_data()
        self.master.after(100, self.update_loop)

    def toggle_all_checkboxes(self, state):
        for var in self.data_vars.values(): var.set(state)
        self._rebuild_plot_area()

    def clear_data(self):
        self.receiver._init_data_history()
        self.data_table.delete(*self.data_table.get_children())
        self._rebuild_plot_area()
        self.status_var.set("数据已清除")

    def on_protocol_updated(self):
        print("主程序接收到协议更新通知。")
        self.receiver.update_protocol_structure()
        self.rebuild_ui_from_protocol()
        pm = ProtocolManager()
        self.protocol_file_var.set(os.path.basename(pm.get_current_protocol_file() or "未命名"))
        messagebox.showinfo("协议更新", "接收端已应用新的协议，数据已清除。")

    def open_protocol_editor(self):
        if self.receiver.running:
            messagebox.showwarning("提示", "请先停止接收再编辑协议。")
            return
        editor_window = tk.Toplevel(self.master)
        editor_window.title("协议编辑器");
        editor_window.geometry("800x600")
        ProtocolEditor(editor_window, protocol_parser, on_save_callback=self.on_protocol_updated)
        editor_window.transient(self.master);
        editor_window.grab_set()

    def update_port_list(self):
        ports = [port.device for port in serial.tools.list_ports.comports()];
        self.port_combobox['values'] = ports if ports else ["无可用串口"]
        if ports: self.port_combobox.current(0)

    def start_receiver(self):
        port = self.port_combobox.get()
        if not port or "无可用串口" in port: messagebox.showerror("错误", "请选择有效串口。");return
        success, message = self.receiver.connect(port, int(self.baudrate_combobox.get()))
        if not success: messagebox.showerror("连接错误", message);return
        self.receiver.start();
        self.status_var.set("正在接收...");
        self.start_button.config(state=tk.DISABLED);
        self.stop_button.config(state=tk.NORMAL)

    def stop_receiver(self):
        self.receiver.stop();
        self.status_var.set("已停止");
        self.start_button.config(state=tk.NORMAL);
        self.stop_button.config(state=tk.DISABLED)

    def update_table(self, parsed_data):
        self.data_table.delete(*self.data_table.get_children())
        for name, value in parsed_data.items():
            try:
                hex_val = f"{int(value):X}"
            except:
                hex_val = "N/A"
            self.data_table.insert("", "end", values=(name, value, hex_val))

    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗?"): self.receiver.stop();self.master.destroy()


def main():
    root = tk.Tk()
    app = ReceiverGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()