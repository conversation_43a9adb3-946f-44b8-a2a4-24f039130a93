# import serial
# import time
# import struct
# import math
# import tkinter as tk
# from tkinter import ttk, messagebox, filedialog
# import threading
# import serial.tools.list_ports
# import sys
# import locale
# import os
# import collections
# import matplotlib.pyplot as plt
# from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
#
# # 导入协议解析器和我们新的信号生成器
# from protocol_parser import ProtocolParser
# from signal_generator import SIGNAL_GENERATORS, SIGNAL_PARAM_DEFINITIONS
#
# # --- 基本配置 ---
# if sys.platform.startswith('win'):
#     try:
#         locale.setlocale(locale.LC_ALL, 'chs')
#     except:
#         pass
#
# plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
# plt.rcParams['axes.unicode_minus'] = False
#
# BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
# DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"
#
# # --- 全局协议解析器实例 ---
# protocol_parser = ProtocolParser()
# if os.path.exists(DEFAULT_PROTOCOL_FILE):
#     protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
# else:
#     print(f"错误: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'。")
#
#
# # --- 主发送逻辑类 ---
# class Protocol422Sender:
#     def __init__(self, port='COM1', baudrate=460800, timeout=1, send_interval=0.005):
#         self.port = port
#         self.baudrate = baudrate
#         self.timeout = timeout
#         self.send_interval = send_interval
#         self.ser = None
#         self.running = False
#         self.frame_counter = 0
#         self.protocol_structure = protocol_parser.get_protocol_structure()
#
#         # signal_params 现在结构更复杂，存储每个指令的类型和参数
#         self.signal_params = {}
#         self.command_fields = []
#         self.update_protocol_structure()
#         self.send_frame()
#
#     def update_protocol_structure(self):
#         """当协议文件重新加载时，更新内部协议结构和参数"""
#         self.protocol_structure = protocol_parser.get_protocol_structure()
#         self.command_fields = [f['name'] for f in self.protocol_structure if '指令' in f['name']]
#         self._init_signal_params()
#         print("发送端协议结构已更新。")
#
#     def _init_signal_params(self):
#         """为每个指令字段初始化默认信号参数"""
#         self.signal_params.clear()
#         # 获取第一个可用的信号类型作为默认值
#         default_signal_type = next(iter(SIGNAL_GENERATORS))
#
#         for field_name in self.command_fields:
#             self.signal_params[field_name] = {
#                 'type': default_signal_type,
#                 'params': SIGNAL_PARAM_DEFINITIONS[default_signal_type].copy()
#             }
#
#     def connect(self):
#         try:
#             self.ser = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
#             return True, f"已连接到 {self.port}, 波特率 {self.baudrate}"
#         except serial.SerialException as e:
#             return False, f"串口连接失败: {e}"
#
#     def disconnect(self):
#         if self.ser and self.ser.is_open: self.ser.close()
#         self.running = False
#         return True, "已断开串口连接"
#
#     def create_frame(self):
#         if not self.protocol_structure: return bytearray()
#
#         last_field = self.protocol_structure[-1]
#         total_bytes = last_field['byte_indices'][-1] + 1
#         frame = bytearray(total_bytes)
#         t = time.time()
#
#         for field in self.protocol_structure:
#             field_name = field['name']
#             value = 0
#             if field_name in self.command_fields:
#                 config = self.signal_params.get(field_name)
#                 if config:
#                     generator_func = SIGNAL_GENERATORS.get(config['type'])
#                     if generator_func:
#                         value = generator_func(config['params'], t)
#             elif field_name == '帧头':
#                 value = 0x7E
#             elif field_name == '帧计数':
#                 value = self.frame_counter
#                 self.frame_counter = (self.frame_counter + 1) % 65536
#             elif field_name == '校验和':
#                 continue
#
#             # 打包
#             start_idx = field['byte_indices'][0]
#             data_type = field['type']
#             try:
#                 value = int(value)
#                 if data_type == 'int16':
#                     struct.pack_into('<h', frame, start_idx, value)
#                 elif data_type == 'uint16':
#                     struct.pack_into('<H', frame, start_idx, value)
#                 elif data_type == 'int8':
#                     struct.pack_into('<b', frame, start_idx, value)
#                 elif data_type == 'uint8':
#                     struct.pack_into('<B', frame, start_idx, value)
#             except (struct.error, ValueError) as e:
#                 print(f"打包字段 '{field_name}' (值: {value}) 时出错: {e}")
#
#         checksum_field = protocol_parser.get_field_by_name('校验和')
#         if checksum_field:
#             checksum_idx = checksum_field['byte_indices'][0]
#             checksum = 0
#             for i in range(2, checksum_idx): checksum ^= frame[i]
#             frame[checksum_idx] = checksum
#
#         return frame
#
#     def start_sending(self):
#         if self.running: return False, "已在发送中"
#         if not self.ser or not self.ser.is_open:
#             success, message = self.connect()
#             if not success: return False, message
#         self.running = True
#         self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
#         self.send_thread.start()
#         return True, "开始发送数据"
#
#     def stop_sending(self):
#         self.running = False
#         if hasattr(self, 'send_thread'): self.send_thread.join(timeout=1.0)
#         return True, "停止发送数据"
#
#     def send_frame(self):
#         """发送一个数据帧"""
#         if not self.ser or not self.ser.is_open:
#             return None, "串口未连接"
#
#         frame = self.create_frame()
#         try:
#             self.ser.write(frame)
#             return frame, f"发送数据帧: {' '.join([f'{b:02X}' for b in frame])}"
#         except serial.SerialException as e:
#             return None, f"发送错误: {e}"
#
#     def _send_loop(self):
#         while self.running:
#             self.send_frame()
#             time.sleep(self.send_interval)
#
#     def get_current_signal_values(self, t):
#         """计算所有指令信号在特定时间t的值，用于绘图"""
#         values = {}
#         for field_name in self.command_fields:
#             config = self.signal_params.get(field_name)
#             if config:
#                 generator_func = SIGNAL_GENERATORS.get(config['type'])
#                 if generator_func:
#                     values[field_name] = generator_func(config['params'], t)
#         return values
#
#
# # --- GUI部分 ---
# class SenderGUI:
#     def __init__(self, master):
#         self.master = master
#         self.master.title("422通讯发送端 (可视化版)")
#         self.master.geometry("1200x800")
#
#         self.sender = Protocol422Sender()
#         self.status_var = tk.StringVar(value="就绪")
#
#         # 用于存储每个指令的UI组件和绘图数据
#         self.command_widgets = {}
#         self.plot_data = {}
#         self.plot_history_size = 100  # 图表显示的数据点数
#
#         self.create_widgets()
#         self.update_port_list()
#         self.rebuild_command_panels()
#
#         # 启动UI更新循环
#         self.master.after(50, self.update_plots)
#
#     def create_widgets(self):
#         # 主布局分为左右两部分
#         paned_window = ttk.PanedWindow(self.master, orient=tk.HORIZONTAL)
#         paned_window.pack(fill=tk.BOTH, expand=True)
#
#         # --- 左侧控制面板 ---
#         left_panel = ttk.Frame(paned_window, width=280)
#         paned_window.add(left_panel, weight=1)
#
#         settings_frame = ttk.LabelFrame(left_panel, text="串口设置")
#         settings_frame.pack(fill=tk.X, padx=5, pady=5)
#
#         ttk.Label(settings_frame, text="串口:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
#         self.port_combobox = ttk.Combobox(settings_frame, width=12)
#         self.port_combobox.grid(row=0, column=1, padx=5, pady=2)
#         ttk.Button(settings_frame, text="刷新", command=self.update_port_list).grid(row=0, column=2, padx=5, pady=2)
#
#         ttk.Label(settings_frame, text="波特率:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
#         self.baudrate_combobox = ttk.Combobox(settings_frame, values=[str(b) for b in BAUDRATES], width=12)
#         self.baudrate_combobox.grid(row=1, column=1, padx=5, pady=2);
#         self.baudrate_combobox.set("460800")
#
#         ttk.Label(settings_frame, text="周期(s):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
#         self.send_interval_entry = ttk.Entry(settings_frame, width=15)
#         self.send_interval_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=2)
#         self.send_interval_entry.insert(0, "0.02")
#
#         ttk.Button(settings_frame, text="应用设置", command=self.apply_settings).grid(row=3, column=0, columnspan=3,
#                                                                                       pady=5)
#
#         control_frame = ttk.LabelFrame(left_panel, text="控制")
#         control_frame.pack(fill=tk.X, padx=5, pady=5)
#         self.start_button = ttk.Button(control_frame, text="开始发送", command=self.start_sender)
#         self.start_button.pack(side=tk.LEFT, padx=5, pady=5)
#         self.stop_button = ttk.Button(control_frame, text="停止发送", command=self.stop_sender, state=tk.DISABLED)
#         self.stop_button.pack(side=tk.LEFT, padx=5, pady=5)
#
#         # --- 右侧指令面板区域 ---
#         right_panel_frame = ttk.Frame(paned_window)
#         paned_window.add(right_panel_frame, weight=4)
#
#         # 创建一个带滚动条的Canvas，用于放置所有指令面板
#         self.cmd_canvas = tk.Canvas(right_panel_frame)
#         scrollbar = ttk.Scrollbar(right_panel_frame, orient="vertical", command=self.cmd_canvas.yview)
#         self.scrollable_frame = ttk.Frame(self.cmd_canvas)
#         self.scrollable_frame.bind("<Configure>",
#                                    lambda e: self.cmd_canvas.configure(scrollregion=self.cmd_canvas.bbox("all")))
#         self.cmd_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
#         self.cmd_canvas.configure(yscrollcommand=scrollbar.set)
#
#         self.cmd_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
#         scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
#
#     def rebuild_command_panels(self):
#         """根据协议中的指令，动态创建或重建所有指令面板"""
#         for widget in self.scrollable_frame.winfo_children(): widget.destroy()
#         self.command_widgets.clear()
#         self.plot_data.clear()
#
#         # 为每个指令字段创建一个专属面板
#         for field_name in self.sender.command_fields:
#             self.plot_data[field_name] = collections.deque(maxlen=self.plot_history_size)
#
#             panel = ttk.LabelFrame(self.scrollable_frame, text=field_name)
#             panel.pack(fill=tk.X, padx=5, pady=5, expand=True)
#
#             # --- 上部：控制区 ---
#             ctrl_area = ttk.Frame(panel)
#             ctrl_area.pack(fill=tk.X, padx=5, pady=5)
#
#             ttk.Label(ctrl_area, text="信号类型:").pack(side=tk.LEFT, padx=(0, 5))
#
#             # 信号类型下拉框
#             signal_type_combo = ttk.Combobox(ctrl_area, values=list(SIGNAL_GENERATORS.keys()), width=10)
#             signal_type_combo.set(self.sender.signal_params[field_name]['type'])
#             signal_type_combo.pack(side=tk.LEFT)
#             signal_type_combo.bind("<<ComboboxSelected>>",
#                                    lambda event, name=field_name: self.on_signal_type_change(name))
#
#             # 参数输入的容器
#             param_frame = ttk.Frame(ctrl_area)
#             param_frame.pack(side=tk.LEFT, fill=tk.X, padx=10)
#
#             # --- 下部：图表区 ---
#             fig, ax = plt.subplots(figsize=(5, 2), dpi=100)
#             fig.tight_layout(pad=2.5)
#             canvas = FigureCanvasTkAgg(fig, master=panel)
#             canvas.get_tk_widget().pack(fill=tk.X, expand=True)
#
#             self.command_widgets[field_name] = {
#                 'panel': panel, 'combo': signal_type_combo,
#                 'param_frame': param_frame, 'param_vars': {},
#                 'fig': fig, 'ax': ax, 'canvas': canvas
#             }
#             # 创建初始的参数输入框
#             self._create_param_entries(field_name)
#
#     def _create_param_entries(self, field_name):
#         """为一个指令动态创建其当前信号类型所需的参数输入框"""
#         widgets = self.command_widgets[field_name]
#         param_frame = widgets['param_frame']
#         widgets['param_vars'].clear()
#
#         for widget in param_frame.winfo_children(): widget.destroy()
#
#         signal_type = widgets['combo'].get()
#         param_defs = SIGNAL_PARAM_DEFINITIONS.get(signal_type, {})
#
#         # 获取当前已保存的参数值，如果不存在则使用默认值
#         current_params = self.sender.signal_params[field_name]['params']
#
#         for param_name, default_value in param_defs.items():
#             ttk.Label(param_frame, text=f"{param_name}:").pack(side=tk.LEFT, padx=(10, 2))
#             var = tk.StringVar(value=str(current_params.get(param_name, default_value)))
#             entry = ttk.Entry(param_frame, textvariable=var, width=8)
#             entry.pack(side=tk.LEFT)
#             widgets['param_vars'][param_name] = var
#
#     def on_signal_type_change(self, field_name):
#         """当用户从下拉框选择了新的信号类型时调用"""
#         widgets = self.command_widgets[field_name]
#         new_type = widgets['combo'].get()
#
#         # 更新sender中的配置
#         self.sender.signal_params[field_name]['type'] = new_type
#         # 重置为新类型的默认参数
#         self.sender.signal_params[field_name]['params'] = SIGNAL_PARAM_DEFINITIONS[new_type].copy()
#
#         # 重建这个指令的参数输入框
#         self._create_param_entries(field_name)
#
#     def apply_settings(self):
#         """应用所有UI上的设置到sender实例"""
#         try:
#             # 应用串口和周期设置
#             interval = float(self.send_interval_entry.get())
#             if interval <= 0: interval = 0.001
#             self.sender.send_interval = interval
#             self.sender.baudrate = int(self.baudrate_combobox.get())
#
#             # 应用所有指令的信号参数
#             for name, widgets in self.command_widgets.items():
#                 params = self.sender.signal_params[name]['params']
#                 for param_name, var in widgets['param_vars'].items():
#                     params[param_name] = float(var.get())
#
#             self.status_var.set("设置已应用")
#             return True
#         except Exception as e:
#             messagebox.showerror("输入错误", f"请输入有效的数值: {e}")
#             return False
#
#     def update_plots(self):
#         """定时器回调，用于更新所有图表"""
#         if self.sender.running:
#             # 获取当前所有指令信号的值
#             current_values = self.sender.get_current_signal_values(time.time())
#
#             for name, value in current_values.items():
#                 # 更新绘图数据
#                 self.plot_data[name].append(value)
#
#                 # 更新对应的图表
#                 widgets = self.command_widgets.get(name)
#                 if widgets:
#                     ax = widgets['ax']
#                     ax.clear()
#                     ax.plot(list(self.plot_data[name]))
#                     ax.set_title(f"{name} - 当前值: {value:.2f}", fontsize=10)
#                     ax.grid(True)
#                     widgets['canvas'].draw()
#
#         # 安排下一次更新
#         self.master.after(50, self.update_plots)
#
#     def start_sender(self):
#         port = self.port_combobox.get()
#         if not port or "无可用串口" in port:
#             messagebox.showerror("错误", "请选择一个有效的串口。")
#             return
#         if not self.apply_settings(): return
#
#         self.sender.port = port
#         success, message = self.sender.start_sending()
#         if success:
#             self.status_var.set("正在发送...")
#             self.start_button.config(state=tk.DISABLED)
#             self.stop_button.config(state=tk.NORMAL)
#         else:
#             messagebox.showerror("启动错误", message)
#
#     def stop_sender(self):
#         self.sender.stop_sending()
#         self.status_var.set("已停止发送")
#         self.start_button.config(state=tk.NORMAL)
#         self.stop_button.config(state=tk.DISABLED)
#
#     def update_port_list(self):
#         ports = [port.device for port in serial.tools.list_ports.comports()]
#         self.port_combobox['values'] = ports if ports else ["无可用串口"]
#         if ports: self.port_combobox.current(0)
#
#     def on_closing(self):
#         if messagebox.askokcancel("退出", "确定要退出吗?"):
#             self.sender.stop_sending()
#             self.sender.disconnect()
#             self.master.destroy()
#
#
# def main():
#     root = tk.Tk()
#     app = SenderGUI(root)
#     root.protocol("WM_DELETE_WINDOW", app.on_closing)
#     root.mainloop()
#
#
# if __name__ == "__main__":
#     main()


import serial
import time
import struct
import math
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import serial.tools.list_ports
import sys
import locale
import os
import collections
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 导入协议解析器和我们新的信号生成器
from protocol_parser import ProtocolParser
from signal_generator import SIGNAL_GENERATORS, SIGNAL_PARAM_DEFINITIONS

# --- 基本配置 ---
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

# --- 全局协议解析器实例 ---
protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
else:
    print(f"错误: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'。")


# --- 主发送逻辑类 ---
class Protocol422Sender:
    def __init__(self, port='COM1', baudrate=460800, timeout=1, send_interval=0.005):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.send_interval = send_interval
        self.ser = None
        self.running = False
        self.frame_counter = 0
        self.protocol_structure = protocol_parser.get_protocol_structure()

        self.signal_params = {}
        self.command_fields = []
        self.update_protocol_structure()

    def update_protocol_structure(self):
        """当协议文件重新加载时，更新内部协议结构和参数"""
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.command_fields = [f['name'] for f in self.protocol_structure if '指令' in f['name']]
        self._init_signal_params()
        print("发送端协议结构已更新。")

    def _init_signal_params(self):
        """为每个指令字段初始化默认信号参数"""
        self.signal_params.clear()
        default_signal_type = next(iter(SIGNAL_GENERATORS))

        for field_name in self.command_fields:
            self.signal_params[field_name] = {
                'type': default_signal_type,
                'params': SIGNAL_PARAM_DEFINITIONS[default_signal_type].copy()
            }

    def connect(self):
        try:
            self.ser = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
            return True, f"已连接到 {self.port}, 波特率 {self.baudrate}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"

    def disconnect(self):
        if self.ser and self.ser.is_open: self.ser.close()
        self.running = False
        return True, "已断开串口连接"

    def create_frame(self):
        if not self.protocol_structure: return bytearray()

        last_field = self.protocol_structure[-1]
        total_bytes = last_field['byte_indices'][-1] + 1
        frame = bytearray(total_bytes)
        t = time.time()

        for field in self.protocol_structure:
            field_name = field['name']
            value = 0

            # !! BUG修复：对帧头进行特殊处理，确保正确打包 !!
            if field_name == '帧头':
                # 使用 struct.pack_into 直接将 0x55AA 按小端序（little-endian）写入帧的起始位置
                # 这会产生 b'\xaa\x55'，与接收端匹配
                struct.pack_into('<H', frame, 0, 0x55AA)
                continue  # 处理完后跳过后面的通用逻辑

            if field_name in self.command_fields:
                config = self.signal_params.get(field_name)
                if config:
                    generator_func = SIGNAL_GENERATORS.get(config['type'])
                    if generator_func:
                        value = generator_func(config['params'], t)
            elif field_name == '帧头':
                value = 0x55AA
            elif field_name == '帧计数':
                value = self.frame_counter
                self.frame_counter = (self.frame_counter + 1) % 65536
            elif field_name == '校验和':
                continue

            start_idx = field['byte_indices'][0]
            data_type = field['type']
            try:
                value = int(value)
                if data_type == 'int16':
                    struct.pack_into('<h', frame, start_idx, value)
                elif data_type == 'uint16':
                    struct.pack_into('<H', frame, start_idx, value)
                elif data_type == 'int8':
                    struct.pack_into('<b', frame, start_idx, value)
                elif data_type == 'uint8':
                    struct.pack_into('<B', frame, start_idx, value)
            except (struct.error, ValueError) as e:
                print(f"打包字段 '{field_name}' (值: {value}) 时出错: {e}")

        checksum_field = protocol_parser.get_field_by_name('校验和')
        if checksum_field:
            checksum_idx = checksum_field['byte_indices'][0]
            checksum = 0
            for i in range(2, checksum_idx): checksum ^= frame[i]
            frame[checksum_idx] = checksum

        return frame

    # !! BUG修复：重新添加被误删的 send_frame 方法 !!
    def send_frame(self):
        """
        创建并发送一个数据帧。
        这是发送线程中调用的核心方法。
        """
        if not self.ser or not self.ser.is_open:
            print("发送失败：串口未连接")
            return

        frame_to_send = self.create_frame()
        try:
            self.ser.write(frame_to_send)
        except serial.SerialException as e:
            print(f"串口写入错误: {e}")
            # 发生错误时可以考虑停止发送
            self.stop_sending()

    def start_sending(self):
        if self.running: return False, "已在发送中"
        if not self.ser or not self.ser.is_open:
            success, message = self.connect()
            if not success: return False, message
        self.running = True
        self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
        self.send_thread.start()
        return True, "开始发送数据"

    def stop_sending(self):
        self.running = False
        if hasattr(self, 'send_thread'): self.send_thread.join(timeout=1.0)
        return True, "停止发送数据"

    def _send_loop(self):
        while self.running:
            self.send_frame()  # 现在这个调用是有效的了
            time.sleep(self.send_interval)

    def get_current_signal_values(self, t):
        """计算所有指令信号在特定时间t的值，用于绘图"""
        values = {}
        for field_name in self.command_fields:
            config = self.signal_params.get(field_name)
            if config:
                generator_func = SIGNAL_GENERATORS.get(config['type'])
                if generator_func:
                    values[field_name] = generator_func(config['params'], t)
        return values


# --- GUI部分 (此处代码与上一版相同，无需修改，为完整性一并提供) ---
class SenderGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯发送端 (可视化版)")
        self.master.geometry("1200x800")

        self.sender = Protocol422Sender()
        self.status_var = tk.StringVar(value="就绪")

        self.command_widgets = {}
        self.plot_data = {}
        self.plot_history_size = 100

        self.create_widgets()
        self.update_port_list()
        self.rebuild_command_panels()

        self.master.after(50, self.update_plots)

    def create_widgets(self):
        paned_window = ttk.PanedWindow(self.master, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        left_panel = ttk.Frame(paned_window, width=280)
        paned_window.add(left_panel, weight=1)

        settings_frame = ttk.LabelFrame(left_panel, text="串口设置")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(settings_frame, text="串口:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.port_combobox = ttk.Combobox(settings_frame, width=12)
        self.port_combobox.grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(settings_frame, text="刷新", command=self.update_port_list).grid(row=0, column=2, padx=5, pady=2)

        ttk.Label(settings_frame, text="波特率:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.baudrate_combobox = ttk.Combobox(settings_frame, values=[str(b) for b in BAUDRATES], width=12)
        self.baudrate_combobox.grid(row=1, column=1, padx=5, pady=2);
        self.baudrate_combobox.set("460800")

        ttk.Label(settings_frame, text="周期(s):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.send_interval_entry = ttk.Entry(settings_frame, width=15)
        self.send_interval_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=2)
        self.send_interval_entry.insert(0, "0.02")

        ttk.Button(settings_frame, text="应用设置", command=self.apply_settings).grid(row=3, column=0, columnspan=3,
                                                                                      pady=5)

        control_frame = ttk.LabelFrame(left_panel, text="控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        self.start_button = ttk.Button(control_frame, text="开始发送", command=self.start_sender)
        self.start_button.pack(side=tk.LEFT, padx=5, pady=5)
        self.stop_button = ttk.Button(control_frame, text="停止发送", command=self.stop_sender, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5, pady=5)

        right_panel_frame = ttk.Frame(paned_window)
        paned_window.add(right_panel_frame, weight=4)

        self.cmd_canvas = tk.Canvas(right_panel_frame)
        scrollbar = ttk.Scrollbar(right_panel_frame, orient="vertical", command=self.cmd_canvas.yview)
        self.scrollable_frame = ttk.Frame(self.cmd_canvas)
        self.scrollable_frame.bind("<Configure>",
                                   lambda e: self.cmd_canvas.configure(scrollregion=self.cmd_canvas.bbox("all")))
        self.cmd_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.cmd_canvas.configure(yscrollcommand=scrollbar.set)

        self.cmd_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def rebuild_command_panels(self):
        for widget in self.scrollable_frame.winfo_children(): widget.destroy()
        self.command_widgets.clear()
        self.plot_data.clear()

        for field_name in self.sender.command_fields:
            self.plot_data[field_name] = collections.deque(maxlen=self.plot_history_size)

            panel = ttk.LabelFrame(self.scrollable_frame, text=field_name)
            panel.pack(fill=tk.X, padx=5, pady=5, expand=True)

            ctrl_area = ttk.Frame(panel)
            ctrl_area.pack(fill=tk.X, padx=5, pady=5)

            ttk.Label(ctrl_area, text="信号类型:").pack(side=tk.LEFT, padx=(0, 5))

            signal_type_combo = ttk.Combobox(ctrl_area, values=list(SIGNAL_GENERATORS.keys()), width=10)
            signal_type_combo.set(self.sender.signal_params[field_name]['type'])
            signal_type_combo.pack(side=tk.LEFT)
            signal_type_combo.bind("<<ComboboxSelected>>",
                                   lambda event, name=field_name: self.on_signal_type_change(name))

            param_frame = ttk.Frame(ctrl_area)
            param_frame.pack(side=tk.LEFT, fill=tk.X, padx=10)

            fig, ax = plt.subplots(figsize=(5, 2), dpi=100)
            fig.tight_layout(pad=2.5)
            canvas = FigureCanvasTkAgg(fig, master=panel)
            canvas.get_tk_widget().pack(fill=tk.X, expand=True)

            self.command_widgets[field_name] = {
                'panel': panel, 'combo': signal_type_combo,
                'param_frame': param_frame, 'param_vars': {},
                'fig': fig, 'ax': ax, 'canvas': canvas
            }
            self._create_param_entries(field_name)

    def _create_param_entries(self, field_name):
        widgets = self.command_widgets[field_name]
        param_frame = widgets['param_frame']
        widgets['param_vars'].clear()

        for widget in param_frame.winfo_children(): widget.destroy()

        signal_type = widgets['combo'].get()
        param_defs = SIGNAL_PARAM_DEFINITIONS.get(signal_type, {})

        current_params = self.sender.signal_params[field_name]['params']

        for param_name, default_value in param_defs.items():
            ttk.Label(param_frame, text=f"{param_name}:").pack(side=tk.LEFT, padx=(10, 2))
            var = tk.StringVar(value=str(current_params.get(param_name, default_value)))
            entry = ttk.Entry(param_frame, textvariable=var, width=8)
            entry.pack(side=tk.LEFT)
            widgets['param_vars'][param_name] = var

    def on_signal_type_change(self, field_name):
        widgets = self.command_widgets[field_name]
        new_type = widgets['combo'].get()

        self.sender.signal_params[field_name]['type'] = new_type
        self.sender.signal_params[field_name]['params'] = SIGNAL_PARAM_DEFINITIONS[new_type].copy()

        self._create_param_entries(field_name)

    def apply_settings(self):
        try:
            interval = float(self.send_interval_entry.get())
            if interval <= 0: interval = 0.001
            self.sender.send_interval = interval
            self.sender.baudrate = int(self.baudrate_combobox.get())

            for name, widgets in self.command_widgets.items():
                params = self.sender.signal_params[name]['params']
                for param_name, var in widgets['param_vars'].items():
                    params[param_name] = float(var.get())

            self.status_var.set("设置已应用")
            return True
        except Exception as e:
            messagebox.showerror("输入错误", f"请输入有效的数值: {e}")
            return False

    def update_plots(self):
        if self.sender.running:
            current_values = self.sender.get_current_signal_values(time.time())

            for name, value in current_values.items():
                self.plot_data[name].append(value)

                widgets = self.command_widgets.get(name)
                if widgets:
                    ax = widgets['ax']
                    ax.clear()
                    ax.plot(list(self.plot_data[name]))
                    ax.set_title(f"{name} - 当前值: {value:.2f}", fontsize=10)
                    ax.grid(True)
                    widgets['canvas'].draw()

        self.master.after(50, self.update_plots)

    def start_sender(self):
        port = self.port_combobox.get()
        if not port or "无可用串口" in port:
            messagebox.showerror("错误", "请选择一个有效的串口。")
            return
        if not self.apply_settings(): return

        self.sender.port = port
        success, message = self.sender.start_sending()
        if success:
            self.status_var.set("正在发送...")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        else:
            messagebox.showerror("启动错误", message)

    def stop_sender(self):
        self.sender.stop_sending()
        self.status_var.set("已停止发送")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def update_port_list(self):
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combobox['values'] = ports if ports else ["无可用串口"]
        if ports: self.port_combobox.current(0)

    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            self.sender.stop_sending()
            self.sender.disconnect()
            self.master.destroy()


def main():
    root = tk.Tk()
    app = SenderGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()