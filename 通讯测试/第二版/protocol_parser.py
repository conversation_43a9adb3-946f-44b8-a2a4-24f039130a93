import csv
import os
import sys

class ProtocolParser:
    def __init__(self):
        """初始化协议解析器"""
        # 协议结构，包含各个字段的定义
        self.protocol_structure = []
        
        # 字节索引到字段的映射
        self.byte_to_field_map = {}
    
    def load_from_csv(self, file_path):
        """从CSV文件加载协议定义"""
        try:
            if not os.path.exists(file_path):
                print(f"错误: 文件 {file_path} 不存在")
                return False
            
            # 清空现有协议结构
            self.protocol_structure = []
            self.byte_to_field_map = {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                # 跳过标题行
                next(reader, None)
                
                for row in reader:
                    if len(row) < 5:  # 至少需要5列
                        continue
                    
                    # 解析字节索引
                    byte_idx_str = row[0].strip()
                    byte_indices = []
                    
                    if '-' in byte_idx_str:
                        # 处理范围，如 "2 - 3"
                        start_idx, end_idx = byte_idx_str.split('-')
                        start_idx = int(start_idx.strip())
                        end_idx = int(end_idx.strip())
                        byte_indices = list(range(start_idx, end_idx + 1))
                        byte_index = start_idx  # 起始字节索引
                        length = end_idx - start_idx + 1
                    else:
                        # 单个字节
                        byte_index = int(byte_idx_str)
                        byte_indices = [byte_index]
                        length = 1
                    
                    # 解析数据类型
                    data_type = row[3].strip().lower().split()[0] if len(row) > 3 else 'uint8'
                    
                    # 对于16位数据类型，确保长度至少为2
                    if data_type in ['int16', 'uint16'] and length < 2:
                        if len(byte_indices) == 1:
                            byte_indices.append(byte_index + 1)
                        length = 2
                    
                    # 创建字段定义
                    field = {
                        'name': row[1].strip(),
                        'byte_index': byte_index,
                        'byte_indices': byte_indices,
                        'length': length,
                        'type': data_type,
                        'unit': row[2].strip() if len(row) > 2 else '',
                        'description': row[4].strip() if len(row) > 4 else ''
                    }
                    
                    # 添加到协议结构
                    self.protocol_structure.append(field)
                    
                    # 更新字节索引映射
                    for idx in byte_indices:
                        self.byte_to_field_map[idx] = field
            
            return True
        except Exception as e:
            print(f"加载协议文件错误: {e}")
            return False
    
    def save_to_csv(self, file_path):
        """保存协议定义到CSV文件"""
        try:
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                # 写入标题行
                writer.writerow(['字节序号', '内容', '单位', '数据类型及长度', '描述'])
                
                # 写入各字段定义
                for field in self.protocol_structure:
                    byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
                    
                    # 格式化字节索引
                    if len(byte_indices) == 1:
                        byte_idx_str = str(byte_indices[0])
                    else:
                        byte_idx_str = f"{byte_indices[0]} - {byte_indices[-1]}"
                    
                    # 写入一行
                    writer.writerow([
                        byte_idx_str,
                        field.get('name', ''),
                        field.get('unit', ''),
                        field.get('type', ''),
                        field.get('description', '')
                    ])
            
            return True
        except Exception as e:
            print(f"保存协议文件错误: {e}")
            return False
    
    def get_protocol_structure(self):
        """获取协议结构"""
        return self.protocol_structure
    
    def get_field_by_name(self, field_name):
        """根据字段名称获取字段定义"""
        for field in self.protocol_structure:
            if field.get('name') == field_name:
                return field
        return None
    
    def get_field_by_byte_index(self, byte_index):
        """根据字节索引获取字段定义"""
        return self.byte_to_field_map.get(byte_index)
    
    def parse_frame(self, frame_data):
        """解析数据帧"""
        if not frame_data or len(frame_data) == 0:
            return {}
        
        result = {}
        
        # 遍历协议结构，解析每个字段
        for field in self.protocol_structure:
            field_name = field.get('name')
            data_type = field.get('type', 'uint8')
            byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
            
            # 确保索引有效
            valid_indices = [idx for idx in byte_indices if idx < len(frame_data)]
            if not valid_indices:
                continue
            
            # 获取字段起始位置
            start_idx = min(valid_indices)
            length = field.get('length', 1)
            
            # 确保不超出数据长度
            if start_idx + length > len(frame_data):
                length = len(frame_data) - start_idx
            
            # 根据数据类型解析值
            try:
                if data_type == 'int16' and length >= 2:
                    import struct
                    result[field_name] = struct.unpack('<h', frame_data[start_idx:start_idx+2])[0]
                elif data_type == 'uint16' and length >= 2:
                    import struct
                    result[field_name] = struct.unpack('<H', frame_data[start_idx:start_idx+2])[0]
                elif data_type in ['uint8', 'hex']:
                    result[field_name] = frame_data[start_idx]
                else:
                    # 未知类型，默认按字节处理
                    result[field_name] = frame_data[start_idx:start_idx+length]
            except Exception as e:
                print(f"解析字段 {field_name} 错误: {e}")
                result[field_name] = None
        
        return result 