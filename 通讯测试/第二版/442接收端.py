import serial
import struct
import time
import threading
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import queue
import serial.tools.list_ports
import sys
import locale
import os
from protocol_parser import ProtocolParser

# 设置中文显示
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']  # 中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
plt.rcParams['font.family'] = 'sans-serif'  # 使用无衬线字体

# 默认协议文件路径
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

# 定义可用波特率选项
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]

# 初始化协议解析器
protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
else:
    # 如果CSV文件不存在，显示错误信息
    print("错误: 未找到协议定义文件。请创建protocol_definition.csv文件。")

# 获取协议结构
PROTOCOL_STRUCTURE = protocol_parser.get_protocol_structure()

# 协议管理器类 - 简化版，只保留加载功能
class ProtocolManager:
    def __init__(self):
        self.protocol_parser = protocol_parser
        
        # 加载默认协议文件
        if os.path.exists(DEFAULT_PROTOCOL_FILE):
            self.current_protocol_file = os.path.abspath(DEFAULT_PROTOCOL_FILE)
        else:
            self.current_protocol_file = None
    
    def load_protocol(self, file_path=None):
        """加载协议文件"""
        if file_path is None:
            file_path = filedialog.askopenfilename(
                title="选择协议文件",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
        
        if file_path and os.path.exists(file_path):
            if self.protocol_parser.load_from_csv(file_path):
                self.current_protocol_file = os.path.abspath(file_path)
                return True, f"已加载协议文件: {os.path.basename(file_path)}"
            else:
                return False, "无法加载协议文件，格式可能不正确"
        return False, "未选择协议文件或文件不存在"
    
    def get_current_protocol_file(self):
        """获取当前加载的协议文件路径"""
        if self.current_protocol_file:
            return os.path.basename(self.current_protocol_file)
        return "未加载协议文件"

class Protocol422Receiver:
    def __init__(self, port=None, baudrate=460800, timeout=1):
        """初始化接收端"""
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        
        # 数据缓冲区
        self.buffer = bytearray()
        
        # 数据队列，用于GUI读取
        self.data_queue = queue.Queue(maxsize=100)
        
        # 运行状态
        self.running = False
        
        # 用于保存解析后的数据
        self.data_history = {
            'timestamp': []
        }
        
        # 创建原始帧存储
        self.raw_frames = []
        self.max_raw_frames = 100
        
        # 最大历史记录点数
        self.max_history = 100
        
        # 获取当前协议结构
        self.protocol_structure = protocol_parser.get_protocol_structure()
        
        # 根据协议结构初始化数据历史记录
        self._init_data_history()
    
    def _init_data_history(self):
        """根据协议结构初始化数据历史记录"""
        # 清空现有历史记录
        self.data_history = {
            'timestamp': []
        }
        
        # 为每个字段创建历史记录
        for field in self.protocol_structure:
            field_name = field['name']
            if field_name not in ['帧头', 'ID', '校验和']:  # 排除不需要记录历史的字段
                self.data_history[field_name] = []
    
    def connect(self, port, baudrate=None):
        """连接到串口"""
        try:
            # 如果已经连接，先断开
            if self.ser and self.ser.is_open:
                self.ser.close()
                time.sleep(0.2)  # 等待串口释放
            
            self.port = port
            if baudrate is not None:
                self.baudrate = baudrate
                
            self.ser = serial.Serial(
                port=port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            
            # 清空缓冲区
            self.buffer = bytearray()
            
            return True, f"已连接到串口 {port}，波特率 {self.baudrate}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"
    
    def set_baudrate(self, baudrate):
        """设置波特率"""
        if self.running:
            return False, "请先停止接收再修改波特率"
        
        try:
            baudrate = int(baudrate)
            self.baudrate = baudrate
            
            # 如果已连接，更新串口波特率
            if self.ser and self.ser.is_open:
                self.ser.baudrate = baudrate
            
            return True, f"波特率已设置为 {baudrate}"
        except ValueError:
            return False, "无效的波特率值"
        except Exception as e:
            return False, f"设置波特率错误: {e}"
    
    def start(self):
        """启动接收线程"""
        if self.running:
            return False, "接收线程已在运行"
        
        if not self.ser or not self.ser.is_open:
            return False, "串口未连接"
        
        # 重新初始化数据历史记录
        self._init_data_history()
        
        self.running = True
        self.receive_thread = threading.Thread(target=self._receive_loop)
        self.receive_thread.daemon = True
        self.receive_thread.start()
        
        return True, "开始接收数据"
    
    def stop(self):
        """停止接收线程"""
        self.running = False
        if hasattr(self, 'receive_thread'):
            self.receive_thread.join(timeout=1.0)
        
        return True, "停止接收数据"
    
    def _receive_loop(self):
        """接收数据循环"""
        while self.running:
            try:
                if self.ser and self.ser.is_open:
                    # 读取可用数据
                    available = self.ser.in_waiting
                    if available > 0:
                        data = self.ser.read(available)
                        self.buffer.extend(data)
                        
                        # 处理缓冲区数据
                        self._process_buffer()
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.001)
            except Exception as e:
                print(f"接收数据错误: {e}")
    
    def _process_buffer(self):
        """处理缓冲区数据"""
        # 获取协议解析器
        parser = protocol_parser
        
        # 循环处理缓冲区中的所有完整帧
        while len(self.buffer) > 0:
            try:
                # 查找帧头
                frame_start = self.buffer.find(0x7E)
                if frame_start == -1:
                    # 未找到帧头，清空缓冲区
                    self.buffer = bytearray()
                    break
                
                # 如果帧头不在开始位置，丢弃前面的数据
                if frame_start > 0:
                    self.buffer = self.buffer[frame_start:]
                    continue
                
                # 获取帧头字段信息
                header_field = parser.get_field_by_name('帧头')
                if not header_field:
                    print("错误: 协议中未定义帧头字段")
                    self.buffer.pop(0)  # 移除一个字节继续处理
                    continue
                
                # 获取校验和字段信息
                checksum_field = parser.get_field_by_name('校验和')
                if not checksum_field:
                    print("错误: 协议中未定义校验和字段")
                    self.buffer.pop(0)
                    continue
                
                # 计算帧总长度
                checksum_byte_idx = max(checksum_field.get('byte_indices', [checksum_field.get('byte_index', 0)]))
                frame_length = checksum_byte_idx + 1
                
                # 检查帧是否完整
                if len(self.buffer) < frame_length + 1:  # +1是为了包含校验和字节
                    break  # 帧不完整，等待更多数据
                
                # 提取完整的帧
                raw_frame = self.buffer[:frame_length + 1]
                
                # 解析帧数据
                parsed_data = {}
                
                # 遍历协议结构，解析每个字段
                for field in parser.get_protocol_structure():
                    field_name = field['name']
                    data_type = field.get('type', 'uint8')
                    byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
                    length = field.get('length', 1)
                    
                    # 校验和会在后面单独处理
                    if field_name == '校验和':
                        continue
                    
                    # 获取字段起始位置
                    start_idx = min(byte_indices)
                    
                    # 确保索引有效
                    if start_idx >= len(raw_frame) or start_idx + length > len(raw_frame):
                        print(f"错误: 字段 {field_name} 的索引超出帧长度")
                        continue
                    
                    # 根据数据类型解析值
                    if data_type == 'int16':
                        parsed_data[field_name] = struct.unpack('<h', raw_frame[start_idx:start_idx+length])[0]
                    elif data_type == 'uint16':
                        parsed_data[field_name] = struct.unpack('<H', raw_frame[start_idx:start_idx+length])[0]
                    elif data_type in ['uint8', 'hex']:
                        parsed_data[field_name] = raw_frame[start_idx]
                    else:
                        # 未知类型，默认按字节处理
                        parsed_data[field_name] = raw_frame[start_idx:start_idx+length]
                
                # 验证校验和 - 修正校验和计算逻辑
                calculated_checksum = 0
                # 帧头后的所有数据进行异或运算，不包括校验和本身
                for i in range(1, frame_length):  # 不包括帧头和校验和本身
                    calculated_checksum ^= raw_frame[i]
                
                # 获取校验和字段的字节索引
                checksum_indices = checksum_field.get('byte_indices', [checksum_field.get('byte_index', 0)])
                checksum_idx = checksum_indices[0]
                
                # 确保校验和索引有效
                if checksum_idx < len(raw_frame):
                    received_checksum = raw_frame[checksum_idx]
                    parsed_data['校验和'] = received_checksum
                    
                    if calculated_checksum == received_checksum:
                        # 校验通过，处理数据
                        timestamp = time.time()
                        
                        # 存储原始数据帧
                        self.raw_frames.append(raw_frame)
                        if len(self.raw_frames) > self.max_raw_frames:
                            self.raw_frames.pop(0)  # 移除最旧的帧
                        
                        # 存储数据
                        self.data_history['timestamp'].append(timestamp)
                        
                        # 动态存储各字段数据
                        for field in parser.get_protocol_structure():
                            field_name = field['name']
                            if field_name not in ['帧头', 'ID', '校验和'] and field_name in self.data_history:
                                self.data_history[field_name].append(parsed_data.get(field_name, 0))
                        
                        # 限制历史记录长度
                        if len(self.data_history['timestamp']) > self.max_history:
                            for key in self.data_history:
                                self.data_history[key] = self.data_history[key][-self.max_history:]
                        
                        # 将数据放入队列供GUI使用
                        data_packet = {
                            'timestamp': timestamp,
                            'raw_frame': raw_frame,
                            'parsed_data': parsed_data
                        }
                        
                        try:
                            self.data_queue.put_nowait(data_packet)
                        except queue.Full:
                            # 队列满，移除最旧的数据
                            try:
                                self.data_queue.get_nowait()
                                self.data_queue.put_nowait(data_packet)
                            except:
                                pass
                        
                        print(f"接收到数据帧: {' '.join([f'{b:02X}' for b in raw_frame])}")
                    else:
                        print(f"校验和错误: 计算值={calculated_checksum:02X}, 接收值={received_checksum:02X}")
                
                # 移除已处理的数据
                self.buffer = self.buffer[frame_length + 1:]
                
            except Exception as e:
                print(f"数据解析错误: {e}")
                self.buffer.pop(0)
    
    def get_latest_data(self):
        """获取最新数据"""
        try:
            return self.data_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_raw_frames(self):
        """获取原始数据帧"""
        return self.raw_frames

class ReceiverGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯接收端可视化")
        self.master.geometry("1200x800")
        
        # 创建接收端实例，但不立即连接串口
        self.receiver = Protocol422Receiver()
        
        # 状态变量
        self.status_var = tk.StringVar(value="就绪")
        self.baudrate_var = tk.StringVar(value="460800")
        
        # 协议管理器
        self.protocol_manager = ProtocolManager()
        self.protocol_file_var = tk.StringVar(value=self.protocol_manager.get_current_protocol_file())
        
        # 数据存储 - 动态根据协议定义创建
        self.data_history = {
            'timestamp': [],
            'raw_frames': []
        }
        
        # 为协议中的每个字段创建数据历史记录
        for field in PROTOCOL_STRUCTURE:
            field_name = field['name']
            if field_name not in ['帧头', 'ID', '校验和']:  # 排除不需要绘图的字段
                self.data_history[field_name] = []
        
        # 颜色列表
        self.colors = ['blue', 'red', 'green', 'purple', 'orange', 'cyan', 'magenta', 'brown', 'pink', 'darkgreen', 
                  'darkblue', 'darkred', 'goldenrod', 'indigo', 'navy', 'crimson', 'teal', 'olive', 'maroon']
        
        # 创建数据显示配置与选择变量
        self.display_config = {}
        self.data_checkboxes = {}
        self.data_vars = {}
        
        # 最大历史记录点数
        self.max_history = 100
        
        # 最后接收到的原始帧
        self.last_raw_frame = None
        
        # 创建GUI组件
        self.create_widgets()
        
        # 获取可用串口列表
        self.update_port_list()
        
        # 启动定时器更新GUI
        self.master.after(100, self.update_gui)
    
    def update_port_list(self):
        """更新可用串口列表"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combobox['values'] = ports
            self.port_combobox.current(0)  # 选择第一个串口
        else:
            self.port_combobox['values'] = ["无可用串口"]
            self.port_combobox.current(0)
            self.status_var.set("未检测到可用串口")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.master)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建菜单栏
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载协议文件", command=self.load_protocol_file)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 创建分隔控件
        self.paned_window = ttk.PanedWindow(self.main_frame, orient=tk.VERTICAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 上部控制和图表区域
        top_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(top_frame, weight=3)
        
        # 下部数据显示区域
        bottom_frame = ttk.Frame(self.paned_window)
        self.paned_window.add(bottom_frame, weight=1)
        
        # ===== 上部控制和图表区域 =====
        # 创建控制框架
        control_frame = ttk.Frame(top_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)
        
        # 串口设置
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=5)
        self.port_combobox = ttk.Combobox(control_frame, width=10)
        self.port_combobox.pack(side=tk.LEFT, padx=5)
        
        # 波特率设置
        ttk.Label(control_frame, text="波特率:").pack(side=tk.LEFT, padx=5)
        self.baudrate_combobox = ttk.Combobox(control_frame, textvariable=self.baudrate_var, 
                                              values=[str(b) for b in BAUDRATES], width=10)
        self.baudrate_combobox.pack(side=tk.LEFT, padx=5)
        self.baudrate_combobox.current(6)  # 默认选择460800
        
        # 刷新串口按钮
        self.refresh_button = ttk.Button(control_frame, text="刷新", command=self.update_port_list)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 启动/停止按钮
        self.start_button = ttk.Button(control_frame, text="启动接收", command=self.start_receiver)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止接收", command=self.stop_receiver)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 清除按钮
        self.clear_button = ttk.Button(control_frame, text="清除数据", command=self.clear_data)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        ttk.Label(control_frame, text="状态:").pack(side=tk.LEFT, padx=5)
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # 协议文件标签
        ttk.Label(control_frame, text="当前协议:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Label(control_frame, textvariable=self.protocol_file_var).pack(side=tk.LEFT, padx=5)
        
        # 创建图表和数据选择区域
        chart_select_frame = ttk.Frame(top_frame)
        chart_select_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 数据选择面板
        select_frame = ttk.LabelFrame(chart_select_frame, text="数据选择")
        select_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 创建滚动条
        select_canvas = tk.Canvas(select_frame, width=200)
        select_scrollbar = ttk.Scrollbar(select_frame, orient="vertical", command=select_canvas.yview)
        select_scrollable_frame = ttk.Frame(select_canvas)
        
        select_scrollable_frame.bind(
            "<Configure>",
            lambda e: select_canvas.configure(scrollregion=select_canvas.bbox("all"))
        )
        
        select_canvas.create_window((0, 0), window=select_scrollable_frame, anchor="nw")
        select_canvas.configure(yscrollcommand=select_scrollbar.set)
        
        select_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        select_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加"全选"和"全不选"按钮
        buttons_frame = ttk.Frame(select_scrollable_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="全选", command=self.select_all_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="全不选", command=self.deselect_all_data).pack(side=tk.LEFT, padx=5)
        
        # 为每个数据字段创建复选框
        self.checkboxes_frame = ttk.Frame(select_scrollable_frame)
        self.checkboxes_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.create_data_checkboxes()
        
        # 图表区域
        chart_frame = ttk.Frame(chart_select_frame)
        chart_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建图表
        self.fig, self.ax = plt.subplots(figsize=(8, 5), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # ===== 下部数据显示区域 =====
        # 原始数据显示
        raw_data_frame = ttk.LabelFrame(bottom_frame, text="原始数据")
        raw_data_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        columns = ("byte_idx", "name", "value", "hex", "binary", "decimal")
        self.data_table = ttk.Treeview(raw_data_frame, columns=columns, show="headings", height=10)
        
        # 设置列标题
        self.data_table.heading("byte_idx", text="字节位置")
        self.data_table.heading("name", text="字段名称")
        self.data_table.heading("value", text="数值")
        self.data_table.heading("hex", text="十六进制")
        self.data_table.heading("binary", text="二进制")
        self.data_table.heading("decimal", text="十进制")
        
        # 设置列宽
        self.data_table.column("byte_idx", width=80)
        self.data_table.column("name", width=200)
        self.data_table.column("value", width=100)
        self.data_table.column("hex", width=100)
        self.data_table.column("binary", width=200)
        self.data_table.column("decimal", width=100)
        
        # 添加表格滚动条
        table_scrollbar = ttk.Scrollbar(raw_data_frame, orient=tk.VERTICAL, command=self.data_table.yview)
        self.data_table.configure(yscrollcommand=table_scrollbar.set)
        
        # 放置表格和滚动条
        self.data_table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        table_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_data_checkboxes(self):
        """创建数据选择复选框"""
        # 清空现有复选框
        for widget in self.checkboxes_frame.winfo_children():
            widget.destroy()
        
        # 清空现有配置
        self.display_config = {}
        self.data_vars = {}
        self.data_checkboxes = {}
        
        # 为每个字段创建复选框
        row = 0
        for field in PROTOCOL_STRUCTURE:
            field_name = field['name']
            if field_name not in ['帧头', 'ID', '校验和']:  # 排除不需要绘图的字段
                # 创建显示变量和复选框
                var = tk.BooleanVar(value=True)
                self.data_vars[field_name] = var
                
                # 分配颜色
                color = self.colors[row % len(self.colors)]
                
                # 创建显示配置
                self.display_config[field_name] = {
                    'show': True,
                    'color': color,
                    'label': field_name
                }
                
                # 创建带颜色指示的复选框
                checkbox_frame = ttk.Frame(self.checkboxes_frame)
                checkbox_frame.grid(row=row, column=0, sticky="w", padx=5, pady=2)
                
                color_indicator = tk.Canvas(checkbox_frame, width=15, height=15, bg=color, highlightthickness=1)
                color_indicator.pack(side=tk.LEFT, padx=(0, 5))
                
                checkbox = ttk.Checkbutton(checkbox_frame, text=field_name, variable=var, 
                                          command=lambda name=field_name: self.toggle_data_display(name))
                checkbox.pack(side=tk.LEFT)
                
                self.data_checkboxes[field_name] = checkbox
                row += 1
    
    def select_all_data(self):
        """选择所有数据显示"""
        for field_name, var in self.data_vars.items():
            var.set(True)
            self.display_config[field_name]['show'] = True
        self.update_chart_data()
    
    def deselect_all_data(self):
        """取消选择所有数据显示"""
        for field_name, var in self.data_vars.items():
            var.set(False)
            self.display_config[field_name]['show'] = False
        self.update_chart_data()
    
    def toggle_data_display(self, field_name):
        """切换数据显示状态"""
        show = self.data_vars[field_name].get()
        self.display_config[field_name]['show'] = show
        self.update_chart_data()
    
    def load_protocol_file(self):
        """加载协议文件"""
        success, message = self.protocol_manager.load_protocol()
        
        if success:
            messagebox.showinfo("成功", message)
            
            # 更新全局协议结构
            global PROTOCOL_STRUCTURE
            PROTOCOL_STRUCTURE = protocol_parser.get_protocol_structure()
            
            # 更新接收端的协议结构
            self.receiver.protocol_structure = PROTOCOL_STRUCTURE
            self.receiver._init_data_history()
            
            # 更新协议文件显示
            self.protocol_file_var.set(self.protocol_manager.get_current_protocol_file())
            
            # 重新初始化数据历史记录
            self.data_history = {
                'timestamp': [],
                'raw_frames': []
            }
            
            # 为协议中的每个字段创建数据历史记录
            for field in PROTOCOL_STRUCTURE:
                field_name = field['name']
                if field_name not in ['帧头', 'ID', '校验和']:
                    self.data_history[field_name] = []
            
            # 重新创建数据选择复选框
            self.create_data_checkboxes()
            
            # 清空表格
            for item in self.data_table.get_children():
                self.data_table.delete(item)
            
            # 更新图表
            self.update_chart_data()
        else:
            messagebox.showerror("错误", message)
    
    def start_receiver(self):
        """启动接收器"""
        port = self.port_combobox.get()
        if port in ["", "无可用串口"]:
            messagebox.showerror("错误", "请先选择有效的串口")
            return
        
        # 获取波特率
        try:
            baudrate = int(self.baudrate_var.get())
        except ValueError:
            messagebox.showerror("错误", "无效的波特率")
            return
        
        # 连接串口
        success, message = self.receiver.connect(port, baudrate)
        if not success:
            messagebox.showerror("连接错误", message)
            return
        
        # 启动接收
        success, message = self.receiver.start()
        if success:
            self.status_var.set("正在接收数据")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.port_combobox.config(state=tk.DISABLED)
            self.baudrate_combobox.config(state=tk.DISABLED)
            self.refresh_button.config(state=tk.DISABLED)
        else:
            messagebox.showerror("启动错误", message)
    
    def stop_receiver(self):
        """停止接收器"""
        success, message = self.receiver.stop()
        self.status_var.set("已停止接收")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.port_combobox.config(state=tk.NORMAL)
        self.baudrate_combobox.config(state=tk.NORMAL)
        self.refresh_button.config(state=tk.NORMAL)
    
    def clear_data(self):
        """清除数据"""
        # 清空数据历史记录
        for key in self.data_history:
            self.data_history[key] = []
        
        # 清空表格
        for item in self.data_table.get_children():
            self.data_table.delete(item)
        
        # 更新图表
        self.update_chart_data()
        
        self.status_var.set("数据已清除")
    
    def update_gui(self):
        """更新GUI显示"""
        try:
            # 获取最新数据
            data = self.receiver.get_latest_data()
            if data:
                # 更新数据历史记录
                self.last_raw_frame = data['raw_frame']
                parsed_data = data['parsed_data']
                
                # 更新表格显示
                for item in self.data_table.get_children():
                    self.data_table.delete(item)
                
                # 按字段显示数据
                for field in PROTOCOL_STRUCTURE:
                    field_name = field['name']
                    byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
                    
                    # 格式化字节索引
                    if len(byte_indices) == 1:
                        byte_idx_str = str(byte_indices[0])
                    else:
                        byte_idx_str = f"{byte_indices[0]}-{byte_indices[-1]}"
                    
                    # 获取字段值
                    value = parsed_data.get(field_name, None)
                    if value is not None:
                        # 根据数据类型格式化显示
                        if isinstance(value, int):
                            hex_str = f"0x{value:02X}" if value <= 0xFF else f"0x{value:04X}"
                            bin_str = f"{value:08b}" if value <= 0xFF else f"{value:016b}"
                            dec_str = str(value)
                        elif isinstance(value, bytes) or isinstance(value, bytearray):
                            hex_str = " ".join([f"{b:02X}" for b in value])
                            bin_str = " ".join([f"{b:08b}" for b in value])
                            dec_str = " ".join([str(b) for b in value])
                        else:
                            hex_str = "N/A"
                            bin_str = "N/A"
                            dec_str = "N/A"
                        
                        self.data_table.insert('', 'end', values=(
                            byte_idx_str,
                            field_name,
                            value,
                            hex_str,
                            bin_str,
                            dec_str
                        ))
            
            # 更新图表
            self.update_chart_data()
        except Exception as e:
            print(f"GUI更新错误: {e}")
        
        # 安排下一次更新
        self.master.after(100, self.update_gui)
    
    def update_chart_data(self):
        """更新图表数据"""
        try:
            # 清空当前图表
            self.ax.clear()
            
            # 获取接收器的数据历史记录
            data_history = self.receiver.data_history
            
            # 检查是否有足够的数据点
            if not data_history['timestamp'] or len(data_history['timestamp']) == 0:
                self.canvas.draw()
                try:
                    self.canvas.flush_events()
                except:
                    pass
                return
            
            # 绘制选中的数据序列
            legend_elements = []
            
            for field_name, config in self.display_config.items():
                if config['show'] and field_name in data_history and len(data_history[field_name]) > 0:
                    # 获取数据并绘制
                    data = data_history[field_name]
                    
                    # 确保数据长度匹配
                    plot_len = len(data)
                    if plot_len > 0:
                        # 创建x轴数据点
                        x_data = list(range(plot_len))
                        
                        # 绘制数据
                        line = self.ax.plot(
                            x_data,
                            data,
                            color=config['color'],
                            label=config['label'],
                            marker='.',
                            markersize=3
                        )
                        legend_elements.append(line[0])  # 添加第一条线到图例
            
            # 添加图例
            if legend_elements:
                self.ax.legend(handles=legend_elements, loc='upper right')
            
            # 设置坐标轴标签
            self.ax.set_xlabel('采样点')
            self.ax.set_ylabel('数值')
            self.ax.set_title('通信数据实时显示')
            
            # 设置网格
            self.ax.grid(True)
            
            # 重绘图表
            self.canvas.draw()
            
            # 强制刷新图表显示
            try:
                self.canvas.flush_events()
            except:
                pass
        except Exception as e:
            print(f"图表更新错误: {e}")
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            if hasattr(self, 'receiver'):
                self.receiver.stop()
            self.master.destroy()

def main():
    # 设置中文支持
    try:
        # 尝试设置系统默认编码
        if sys.platform.startswith('win'):
            locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass
    
    root = tk.Tk()
    app = ReceiverGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
