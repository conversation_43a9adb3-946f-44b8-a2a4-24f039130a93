import serial
import time
import struct
import math
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import serial.tools.list_ports
import sys
import locale
import os
from protocol_parser import ProtocolParser

# 设置中文显示
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

# 配置matplotlib支持中文显示（如果将来需要绘图功能）
try:
    import matplotlib.pyplot as plt
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
except ImportError:
    pass

# 定义可用波特率选项
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]

# 默认协议文件路径
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

# 初始化协议解析器
protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
else:
    # 如果CSV文件不存在，显示错误信息
    print("错误: 未找到协议定义文件。请创建protocol_definition.csv文件。")

# 获取协议结构
PROTOCOL_STRUCTURE = protocol_parser.get_protocol_structure()

# 协议管理器类 - 简化版，只保留加载功能
class ProtocolManager:
    def __init__(self):
        self.protocol_parser = protocol_parser
        
        # 加载默认协议文件
        if os.path.exists(DEFAULT_PROTOCOL_FILE):
            self.current_protocol_file = os.path.abspath(DEFAULT_PROTOCOL_FILE)
        else:
            self.current_protocol_file = None
    
    def load_protocol(self, file_path=None):
        """加载协议文件"""
        if file_path is None:
            file_path = filedialog.askopenfilename(
                title="选择协议文件",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
        
        if file_path and os.path.exists(file_path):
            if self.protocol_parser.load_from_csv(file_path):
                self.current_protocol_file = os.path.abspath(file_path)
                return True, f"已加载协议文件: {os.path.basename(file_path)}"
            else:
                return False, "无法加载协议文件，格式可能不正确"
        return False, "未选择协议文件或文件不存在"
    
    def get_current_protocol_file(self):
        """获取当前加载的协议文件路径"""
        if self.current_protocol_file:
            return os.path.basename(self.current_protocol_file)
        return "未加载协议文件"

def generate_sin_value(amplitude, frequency, phase, t, is_switch=False):
    """生成正弦波值"""
    sin_value = amplitude * math.sin(2 * math.pi * frequency * t + phase)
    
    if is_switch:
        # 对于开关信号，使用阈值函数生成0/1
        return 1 if sin_value > 0 else 0
    
    # 对于数值信号，返回整数值
    return int(sin_value)

class Protocol422Sender:
    def __init__(self, port='COM1', baudrate=460800, timeout=1, send_interval=0.005):
        """初始化发送端"""
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.send_interval = send_interval
        self.ser = None
        
        # 运行状态
        self.running = False
        
        # 帧计数器
        self.counter = 0
        
        # 获取当前协议结构
        self.protocol_structure = protocol_parser.get_protocol_structure()
        
        # 创建数据字段与正弦波参数映射
        self.sin_params = {}
        
        # 为每个非固定字段分配不同的正弦波参数
        self._init_sin_params()
    
    def _init_sin_params(self):
        """为每个字段初始化正弦波参数"""
        # 清空现有参数
        self.sin_params = {}
        
        # 固定字段列表，这些字段不需要随时间变化
        fixed_fields = ['帧头', 'ID', '校验和', '帧计数']
        
        # 随机分配不同频率和相位的参数
        field_index = 0
        for field in self.protocol_structure:
            field_name = field['name']
            if field_name not in fixed_fields:
                # 根据数据类型设置不同的振幅
                data_type = field.get('type', 'uint8')
                
                if data_type == 'int16':
                    amplitude = 32767  # 16位有符号整数范围
                elif data_type == 'uint16':
                    amplitude = 65535  # 16位无符号整数范围
                elif data_type == 'int8':
                    amplitude = 127    # 8位有符号整数范围
                elif data_type == 'uint8' or data_type == 'hex':
                    amplitude = 255    # 8位无符号整数范围
                else:
                    amplitude = 100    # 默认振幅
                
                # 为了让曲线看起来不同，设置不同的频率和相位
                frequency = 0.1 + field_index * 0.05  # 0.1Hz 到 ~2Hz 的递增频率
                phase = field_index * 0.2  # 0 到 ~6.28 的递增相位
                
                # 特殊字段可能需要特殊处理
                if '开关' in field_name or '状态' in field_name:
                    # 对于开关信号，使用阈值函数生成0/1
                    self.sin_params[field_name] = {
                        'amplitude': 1,
                        'frequency': 0.05 + field_index * 0.01,
                        'phase': field_index * 0.5,
                        'is_switch': True
                    }
                else:
                    # 普通数值字段
                    self.sin_params[field_name] = {
                        'amplitude': amplitude,
                        'frequency': frequency,
                        'phase': phase,
                        'is_switch': False
                    }
                
                field_index += 1
        
        print(f"已初始化 {field_index} 个字段的正弦波参数")
        
    def connect(self):
        """连接串口"""
        try:
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout
            )
            return True, f"已连接到 {self.port}, 波特率 {self.baudrate}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            self.ser = None
        self.running = False
        return True, "已断开串口连接"
    
    def generate_sin_value(self, amplitude, frequency, phase, t, is_switch=False):
        """生成正弦波值"""
        return generate_sin_value(amplitude, frequency, phase, t, is_switch)
    
    def create_frame(self):
        """创建数据帧"""
        # 检查协议结构是否存在
        if not self.protocol_structure:
            print("错误: 协议结构为空。请先加载或定义协议。")
            return bytearray()
        
        # 创建空数据帧
        frame = bytearray()
        frame_data = {}
        
        # 当前时间，用于生成正弦波值
        t = time.time()
        
        # 遍历协议结构，填充数据帧
        for field in self.protocol_structure:
            field_name = field['name']
            data_type = field.get('type', 'uint8')
            byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
            length = field.get('length', 1)
            
            # 帧头固定为0x7E
            if field_name == '帧头':
                frame.append(0x7E)
                frame_data[field_name] = 0x7E
            # ID固定为0x01
            elif field_name == 'ID':
                frame.append(0x01)
                frame_data[field_name] = 0x01
            # 帧计数
            elif field_name == '帧计数':
                value = self.counter
                # 根据长度决定如何打包帧计数
                if length == 1:
                    frame.append(value & 0xFF)
                else:  # 默认按2字节处理
                    frame.extend(struct.pack('<H', value))
                frame_data[field_name] = value
                self.counter = (self.counter + 1) % 65536  # 循环计数
            # 校验和在所有字段添加完后计算
            elif field_name == '校验和':
                pass
            # 其他字段使用正弦波生成
            else:
                # 获取此字段的正弦波参数
                params = self.sin_params.get(field_name, {
                    'amplitude': 100,
                    'frequency': 0.2,
                    'phase': 0,
                    'is_switch': False
                })
                
                value = self.generate_sin_value(
                    params['amplitude'], 
                    params['frequency'], 
                    params['phase'], 
                    t,
                    params.get('is_switch', False)
                )
                
                # 根据数据类型打包值
                if data_type == 'int16':
                    value = int(value) if not isinstance(value, int) else value
                    value = max(-32768, min(32767, value))  # 限制在int16范围内
                    frame.extend(struct.pack('<h', value))
                elif data_type == 'uint16':
                    value = int(value) if not isinstance(value, int) else value
                    value = max(0, min(65535, value))  # 限制在uint16范围内
                    frame.extend(struct.pack('<H', value))
                elif data_type == 'int8':
                    value = int(value) if not isinstance(value, int) else value
                    value = max(-128, min(127, value))  # 限制在int8范围内
                    frame.append(value & 0xFF)
                elif data_type in ['uint8', 'hex']:
                    value = int(value) if not isinstance(value, int) else value
                    value = max(0, min(255, value))  # 限制在uint8范围内
                    frame.append(value)
                else:
                    # 默认按字节处理
                    if not isinstance(value, int):
                        value = int(value)
                    frame.append(value & 0xFF)
                
                frame_data[field_name] = value
        
        # 计算校验和：不包括帧头的异或和
        checksum = 0
        for i in range(1, len(frame)):
            checksum ^= frame[i]
        
        # 添加校验和字段
        frame.append(checksum)
        frame_data['校验和'] = checksum
        
        return frame
    
    def send_frame(self):
        """发送一个数据帧"""
        if not self.ser or not self.ser.is_open:
            return None, "串口未连接"
            
        frame = self.create_frame()
        try:
            self.ser.write(frame)
            return frame, f"发送数据帧: {' '.join([f'{b:02X}' for b in frame])}"
        except serial.SerialException as e:
            return None, f"发送错误: {e}"
    
    def start_sending(self):
        """开始持续发送数据"""
        if self.running:
            return False, "已经在发送中"
        
        if not self.ser or not self.ser.is_open:
            success, message = self.connect()
            if not success:
                return False, message
        
        self.running = True
        self.send_thread = threading.Thread(target=self._send_loop)
        self.send_thread.daemon = True
        self.send_thread.start()
        return True, "开始发送数据"
    
    def stop_sending(self):
        """停止发送数据"""
        self.running = False
        if hasattr(self, 'send_thread'):
            self.send_thread.join(timeout=1.0)
        return True, "停止发送数据"
    
    def _send_loop(self):
        """发送循环"""
        while self.running:
            frame, message = self.send_frame()
            print(message)
            
            # 按照设定的发送周期
            time.sleep(self.send_interval)
    
    def set_baudrate(self, baudrate):
        """设置波特率"""
        if self.running:
            return False, "请先停止发送再修改波特率"
        
        self.baudrate = baudrate
        return True, f"波特率已设置为 {baudrate}"
    
    def set_send_interval(self, interval):
        """设置发送周期"""
        try:
            interval = float(interval)
            if interval <= 0:
                return False, "发送周期必须大于0"
            
            self.send_interval = interval
            return True, f"发送周期已设置为 {interval}秒"
        except ValueError:
            return False, "无效的发送周期值"

class SenderGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯发送端")
        self.master.geometry("800x600")
        
        # 创建发送端实例
        self.sender = Protocol422Sender()
        
        # 协议管理器
        self.protocol_manager = ProtocolManager()
        self.protocol_file_var = tk.StringVar(value=self.protocol_manager.get_current_protocol_file())
        
        # 状态变量
        self.status_var = tk.StringVar(value="就绪")
        self.baudrate_var = tk.StringVar(value="460800")
        self.send_interval_var = tk.StringVar(value="0.005")
        
        # 创建GUI组件
        self.create_widgets()
        
        # 获取可用串口列表
        self.update_port_list()
        
        # 启动信息更新线程
        self.info_thread = threading.Thread(target=self._update_info_loop)
        self.info_thread.daemon = True
        self.info_thread.start()
    
    def update_port_list(self):
        """更新可用串口列表"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combobox['values'] = ports
            self.port_combobox.current(0)  # 选择第一个串口
        else:
            self.port_combobox['values'] = ["无可用串口"]
            self.port_combobox.current(0)
            self.status_var.set("未检测到可用串口")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建菜单栏
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="加载协议文件", command=self.load_protocol_file)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="串口设置")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 串口设置
        ttk.Label(settings_frame, text="串口:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.port_combobox = ttk.Combobox(settings_frame, width=15)
        self.port_combobox.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 刷新串口按钮
        ttk.Button(settings_frame, text="刷新", command=self.update_port_list).grid(row=0, column=2, padx=5, pady=5)
        
        # 波特率设置
        ttk.Label(settings_frame, text="波特率:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.baudrate_combobox = ttk.Combobox(settings_frame, textvariable=self.baudrate_var, values=[str(b) for b in BAUDRATES], width=15)
        self.baudrate_combobox.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        self.baudrate_combobox.current(6)  # 默认选择460800
        
        # 发送周期设置
        ttk.Label(settings_frame, text="发送周期(秒):").grid(row=2, column=0, padx=5, pady=5, sticky=tk.W)
        self.send_interval_entry = ttk.Entry(settings_frame, textvariable=self.send_interval_var, width=15)
        self.send_interval_entry.grid(row=2, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 应用设置按钮
        ttk.Button(settings_frame, text="应用设置", command=self.apply_settings).grid(row=3, column=0, columnspan=2, padx=5, pady=5)
        
        # 协议文件显示
        ttk.Label(settings_frame, text="当前协议:").grid(row=4, column=0, padx=5, pady=5, sticky=tk.W)
        ttk.Label(settings_frame, textvariable=self.protocol_file_var).grid(row=4, column=1, padx=5, pady=5, sticky=tk.W)
        
        # 创建控制框架
        control_frame = ttk.LabelFrame(main_frame, text="控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 启动/停止按钮
        self.start_button = ttk.Button(control_frame, text="开始发送", command=self.start_sender)
        self.start_button.pack(side=tk.LEFT, padx=10, pady=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止发送", command=self.stop_sender)
        self.stop_button.pack(side=tk.LEFT, padx=10, pady=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 状态标签
        ttk.Label(control_frame, text="状态:").pack(side=tk.LEFT, padx=(20, 5), pady=5)
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5, pady=5)
        
        # 创建信息显示框架
        info_frame = ttk.LabelFrame(main_frame, text="发送信息")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 信息文本框
        self.info_text = scrolledtext.ScrolledText(info_frame, wrap=tk.WORD)
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.info_text.config(state=tk.DISABLED)  # 只读模式
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 设置波特率
            baudrate = int(self.baudrate_var.get())
            success, message = self.sender.set_baudrate(baudrate)
            if not success:
                messagebox.showerror("设置错误", message)
                return
            
            # 设置发送周期
            send_interval = float(self.send_interval_var.get())
            success, message = self.sender.set_send_interval(send_interval)
            if not success:
                messagebox.showerror("设置错误", message)
                return
            
            self.status_var.set("设置已应用")
        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数值")
    
    def start_sender(self):
        """启动发送器"""
        port = self.port_combobox.get()
        if port in ["", "无可用串口"]:
            messagebox.showerror("错误", "请先选择有效的串口")
            return
        
        # 设置发送器参数
        self.sender.port = port
        
        # 应用设置
        self.apply_settings()
        
        # 启动发送
        success, message = self.sender.start_sending()
        if success:
            self.status_var.set("正在发送数据")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.port_combobox.config(state=tk.DISABLED)
            self.baudrate_combobox.config(state=tk.DISABLED)
            self.send_interval_entry.config(state=tk.DISABLED)
        else:
            messagebox.showerror("启动错误", message)
    
    def stop_sender(self):
        """停止发送器"""
        success, message = self.sender.stop_sending()
        self.status_var.set("已停止发送")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.port_combobox.config(state=tk.NORMAL)
        self.baudrate_combobox.config(state=tk.NORMAL)
        self.send_interval_entry.config(state=tk.NORMAL)
    
    def _update_info_loop(self):
        """更新信息循环"""
        last_frame_count = 0
        
        while True:
            try:
                if hasattr(self, 'sender') and self.sender.running:
                    # 获取当前帧计数
                    frame_count = self.sender.counter
                    
                    if frame_count != last_frame_count:
                        # 更新信息文本框
                        self.info_text.config(state=tk.NORMAL)
                        self.info_text.insert(tk.END, f"已发送帧 #{frame_count}\n")
                        
                        # 限制文本框行数
                        if float(self.info_text.index(tk.END)) > 100:  # 超过100行
                            self.info_text.delete(1.0, 2.0)  # 删除第一行
                        
                        self.info_text.see(tk.END)  # 滚动到底部
                        self.info_text.config(state=tk.DISABLED)
                        
                        last_frame_count = frame_count
            except Exception as e:
                print(f"更新信息错误: {e}")
            
            # 休眠一段时间
            time.sleep(0.1)
    
    def load_protocol_file(self):
        """加载协议文件"""
        success, message = self.protocol_manager.load_protocol()
        
        if success:
            messagebox.showinfo("成功", message)
            
            # 更新全局协议结构
            global PROTOCOL_STRUCTURE
            PROTOCOL_STRUCTURE = protocol_parser.get_protocol_structure()
            
            # 更新发送端的协议结构
            self.sender.protocol_structure = PROTOCOL_STRUCTURE
            
            # 重新初始化sin波参数
            self.sender._init_sin_params()
            
            # 更新协议文件显示
            self.protocol_file_var.set(self.protocol_manager.get_current_protocol_file())
        else:
            messagebox.showerror("错误", message)
    
    def on_closing(self):
        """窗口关闭事件"""
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            if hasattr(self, 'sender'):
                self.sender.stop_sending()
            self.master.destroy()

def main():
    # 设置中文支持
    try:
        if sys.platform.startswith('win'):
            locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass
    
    root = tk.Tk()
    app = SenderGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
