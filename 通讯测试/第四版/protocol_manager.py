import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import csv
import sys
import locale
from protocol_parser import ProtocolParser

# 设置中文显示
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

# 配置matplotlib支持中文显示（如果将来需要绘图功能）
try:
    import matplotlib.pyplot as plt
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
except ImportError:
    pass

# 默认协议文件路径
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

class ProtocolManager:
    def __init__(self):
        # 初始化协议解析器
        self.protocol_parser = ProtocolParser()
        
        # 加载默认协议文件
        if os.path.exists(DEFAULT_PROTOCOL_FILE):
            self.protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
            self.current_protocol_file = os.path.abspath(DEFAULT_PROTOCOL_FILE)
        else:
            self.current_protocol_file = None
    
    def load_protocol(self, file_path=None):
        """加载协议文件"""
        if file_path is None:
            file_path = filedialog.askopenfilename(
                title="选择协议文件",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
        
        if file_path and os.path.exists(file_path):
            if self.protocol_parser.load_from_csv(file_path):
                self.current_protocol_file = os.path.abspath(file_path)
                return True, f"已加载协议文件: {os.path.basename(file_path)}"
            else:
                return False, "无法加载协议文件，格式可能不正确"
        return False, "未选择协议文件或文件不存在"
    
    def save_protocol(self, file_path=None):
        """保存协议文件"""
        if file_path is None:
            file_path = filedialog.asksaveasfilename(
                title="保存协议文件",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
            )
        
        if file_path:
            if self.protocol_parser.save_to_csv(file_path):
                self.current_protocol_file = os.path.abspath(file_path)
                return True, f"已保存协议文件: {os.path.basename(file_path)}"
            else:
                return False, "无法保存协议文件"
        return False, "未选择保存位置"
    
    def get_protocol_structure(self):
        """获取当前协议结构"""
        return self.protocol_parser.get_protocol_structure()
    
    def get_current_protocol_file(self):
        """获取当前加载的协议文件路径"""
        return self.current_protocol_file

class FieldDialog(tk.Toplevel):
    def __init__(self, parent, title, field=None):
        super().__init__(parent)
        self.title(title)
        self.geometry("500x400")
        self.resizable(False, False)
        self.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
        # 模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 结果
        self.result = None
        
        # 字段数据
        self.field = field
        
        # 创建界面元素
        self.create_widgets()
        
        # 如果是编辑模式，填充已有数据
        if field:
            self.fill_field_data(field)
        
        # 等待窗口关闭
        self.wait_window(self)
    
    def create_widgets(self):
        # 创建表单
        form_frame = ttk.Frame(self, padding="10")
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # 字节序号
        ttk.Label(form_frame, text="字节序号:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.byte_idx_entry = ttk.Entry(form_frame, width=30)
        self.byte_idx_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(form_frame, text="(单个数字或范围，如'5'或'5-6')").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 字段名称
        ttk.Label(form_frame, text="字段名称:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.name_entry = ttk.Entry(form_frame, width=30)
        self.name_entry.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 单位
        ttk.Label(form_frame, text="单位:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.unit_entry = ttk.Entry(form_frame, width=30)
        self.unit_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 数据类型
        ttk.Label(form_frame, text="数据类型:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.data_type_var = tk.StringVar()
        data_types = ["uint8", "int8", "uint16", "int16", "hex"]
        self.data_type_combobox = ttk.Combobox(form_frame, textvariable=self.data_type_var, values=data_types, width=28)
        self.data_type_combobox.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        self.data_type_combobox.current(0)
        
        # 描述
        ttk.Label(form_frame, text="描述:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.desc_entry = ttk.Entry(form_frame, width=30)
        self.desc_entry.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 按钮
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="确定", command=self.on_ok).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(side=tk.LEFT, padx=10)
    
    def fill_field_data(self, field):
        """填充字段数据"""
        # 字节序号
        byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
        if len(byte_indices) == 1:
            self.byte_idx_entry.insert(0, str(byte_indices[0]))
        else:
            self.byte_idx_entry.insert(0, f"{byte_indices[0]}-{byte_indices[-1]}")
        
        # 字段名称
        self.name_entry.insert(0, field.get('name', ''))
        
        # 单位
        self.unit_entry.insert(0, field.get('unit', ''))
        
        # 数据类型
        data_type = field.get('type', 'uint8')
        if data_type in ["uint8", "int8", "uint16", "int16", "hex"]:
            self.data_type_var.set(data_type)
        else:
            self.data_type_var.set("uint8")
        
        # 描述
        self.desc_entry.insert(0, field.get('description', ''))
    
    def on_ok(self):
        """确定按钮回调"""
        # 验证输入
        byte_idx_str = self.byte_idx_entry.get().strip()
        name = self.name_entry.get().strip()
        unit = self.unit_entry.get().strip()
        data_type = self.data_type_var.get()
        description = self.desc_entry.get().strip()
        
        if not byte_idx_str:
            messagebox.showerror("错误", "请输入字节序号")
            return
        
        if not name:
            messagebox.showerror("错误", "请输入字段名称")
            return
        
        # 解析字节序号
        try:
            if '-' in byte_idx_str:
                start, end = byte_idx_str.split('-')
                start = int(start.strip())
                end = int(end.strip())
                if start > end:
                    start, end = end, start
                byte_indices = list(range(start, end + 1))
                byte_index = start
                length = end - start + 1
            else:
                byte_index = int(byte_idx_str)
                byte_indices = [byte_index]
                length = 1
                
                # 对于16位数据类型，自动扩展为2字节
                if data_type in ["uint16", "int16"] and length < 2:
                    byte_indices = [byte_index, byte_index + 1]
                    length = 2
        except ValueError:
            messagebox.showerror("错误", "字节序号格式不正确")
            return
        
        # 创建字段数据
        self.result = {
            'name': name,
            'byte_index': byte_index,
            'byte_indices': byte_indices,
            'length': length,
            'type': data_type,
            'unit': unit,
            'description': description
        }
        
        self.destroy()
    
    def on_cancel(self):
        """取消按钮回调"""
        self.result = None
        self.destroy()

class ProtocolEditor(ttk.Frame):
    def __init__(self, parent, protocol_parser, on_save_callback=None):
        super().__init__(parent)
        self.parent = parent
        self.parent.title("协议编辑器")
        self.pack(fill=tk.BOTH, expand=True)
        
        self.protocol_parser = protocol_parser
        self.on_save_callback = on_save_callback
        self.protocol_data = []
        
        # 创建界面元素
        self.create_widgets()
        
        # 加载当前协议数据
        self.load_protocol_data()
        
    def create_widgets(self):
        # 创建工具栏
        toolbar_frame = ttk.Frame(self)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(toolbar_frame, text="添加字段", command=self.add_field).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="插入字段", command=self.insert_field).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="删除字段", command=self.delete_field).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="编辑字段", command=self.edit_field).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="保存协议", command=self.save_protocol).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="导入CSV", command=self.import_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="导出CSV", command=self.export_csv).pack(side=tk.LEFT, padx=5)
        
        # 创建表格
        table_frame = ttk.Frame(self)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格列
        columns = ("byte_idx", "name", "unit_type", "data_type", "desc")
        self.table = ttk.Treeview(table_frame, columns=columns, show="headings")
        
        # 设置列标题
        self.table.heading("byte_idx", text="字节序号")
        self.table.heading("name", text="内容")
        self.table.heading("unit_type", text="单位")
        self.table.heading("data_type", text="数据类型及长度")
        self.table.heading("desc", text="描述")
        
        # 设置列宽
        self.table.column("byte_idx", width=80)
        self.table.column("name", width=200)
        self.table.column("unit_type", width=80)
        self.table.column("data_type", width=120)
        self.table.column("desc", width=200)
        
        # 添加表格滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.table.yview)
        self.table.configure(yscrollcommand=scrollbar.set)
        
        # 放置表格和滚动条
        self.table.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.table.bind("<Double-1>", lambda event: self.edit_field())
    
    def load_protocol_data(self):
        """加载协议数据到表格"""
        # 清空表格
        for item in self.table.get_children():
            self.table.delete(item)
        
        # 从解析器中获取协议结构
        self.protocol_data = self.protocol_parser.get_protocol_structure()
        
        # 添加数据到表格
        for field in self.protocol_data:
            # 格式化字节索引
            byte_indices = field.get('byte_indices', [field.get('byte_index', 0)])
            if len(byte_indices) == 1:
                byte_idx_str = str(byte_indices[0])
            else:
                byte_idx_str = f"{byte_indices[0]} - {byte_indices[-1]}"
            
            # 格式化单位和数据类型
            unit = field.get('unit', '')
            
            # 格式化数据类型
            data_type = field.get('type', '')
            length = field.get('length', 1)
            if data_type == 'int16' or data_type == 'uint16':
                data_type_str = '16位整型'
            elif data_type == 'int8' or data_type == 'uint8':
                data_type_str = '8位整型'
            elif data_type == 'hex':
                data_type_str = 'unsigned char'
            else:
                data_type_str = f"{length*8}位整型"
            
            # 添加到表格
            self.table.insert('', 'end', values=(
                byte_idx_str,
                field.get('name', ''),
                unit,
                data_type_str,
                field.get('description', '')
            ))
    
    def add_field(self):
        """添加新字段到末尾"""
        # 创建新字段对话框
        dialog = FieldDialog(self.parent, "添加字段")
        if dialog.result:
            # 添加新字段到协议数据
            field = dialog.result
            self.protocol_data.append(field)
            
            # 更新表格
            self.load_protocol_data()
    
    def insert_field(self):
        """在选定位置插入字段并更新后续字节序号"""
        # 获取选中的项
        selected_item = self.table.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择一个字段，新字段将插入到该字段之前")
            return
        
        # 获取选中项的索引
        item_id = selected_item[0]
        item_index = self.table.index(item_id)
        
        # 创建新字段对话框
        dialog = FieldDialog(self.parent, "插入字段")
        if not dialog.result:
            return
        
        # 获取新字段数据
        new_field = dialog.result
        byte_indices = new_field.get('byte_indices', [])
        if not byte_indices:
            messagebox.showerror("错误", "字段字节索引无效")
            return
        
        # 计算新字段占用的字节数
        inserted_bytes_count = len(byte_indices)
        
        # 插入新字段到选定位置
        self.protocol_data.insert(item_index, new_field)
        
        # 更新后续所有字段的字节索引
        for i in range(item_index + 1, len(self.protocol_data)):
            field = self.protocol_data[i]
            old_indices = field.get('byte_indices', [])
            
            if old_indices:
                # 更新字节索引
                new_indices = [idx + inserted_bytes_count for idx in old_indices]
                field['byte_indices'] = new_indices
                
                # 更新字节索引起始位置
                if 'byte_index' in field:
                    field['byte_index'] = new_indices[0]
        
        # 更新表格
        self.load_protocol_data()
        
        # 选中新插入的字段
        self.table.selection_set(self.table.get_children()[item_index])
        self.table.focus(self.table.get_children()[item_index])
        
        messagebox.showinfo("成功", f"已插入字段并更新了后续{len(self.protocol_data) - item_index - 1}个字段的字节序号")
    
    def edit_field(self):
        """编辑选中的字段"""
        # 获取选中的项
        selected_item = self.table.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择一个字段")
            return
        
        # 获取选中项的索引
        item_id = selected_item[0]
        item_index = self.table.index(item_id)
        
        # 获取对应的字段数据
        if item_index < len(self.protocol_data):
            field = self.protocol_data[item_index]
            old_indices = field.get('byte_indices', [])
            
            # 创建编辑字段对话框
            dialog = FieldDialog(self.parent, "编辑字段", field)
            if dialog.result:
                new_field = dialog.result
                new_indices = new_field.get('byte_indices', [])
                
                # 检查是否需要更新后续字段的字节序号
                if new_indices and old_indices and len(new_indices) != len(old_indices):
                    # 计算字节数变化
                    bytes_diff = len(new_indices) - len(old_indices)
                    
                    # 更新后续所有字段的字节索引
                    if bytes_diff != 0:
                        for i in range(item_index + 1, len(self.protocol_data)):
                            next_field = self.protocol_data[i]
                            next_indices = next_field.get('byte_indices', [])
                            
                            if next_indices:
                                # 更新字节索引
                                updated_indices = [idx + bytes_diff for idx in next_indices]
                                next_field['byte_indices'] = updated_indices
                                
                                # 更新字节索引起始位置
                                if 'byte_index' in next_field:
                                    next_field['byte_index'] = updated_indices[0]
                
                # 更新字段数据
                self.protocol_data[item_index] = new_field
                
                # 刷新表格
                self.load_protocol_data()
                
                # 如果字节数有变化，显示提示
                if new_indices and old_indices and len(new_indices) != len(old_indices):
                    messagebox.showinfo("成功", f"已更新字段并调整了后续{len(self.protocol_data) - item_index - 1}个字段的字节序号")
    
    def delete_field(self):
        """删除选中的字段并更新后续字节序号"""
        # 获取选中的项
        selected_item = self.table.selection()
        if not selected_item:
            messagebox.showinfo("提示", "请先选择一个字段")
            return
        
        # 获取选中项的索引
        item_id = selected_item[0]
        item_index = self.table.index(item_id)
        
        # 获取要删除的字段
        if item_index >= len(self.protocol_data):
            return
            
        field_to_delete = self.protocol_data[item_index]
        byte_indices = field_to_delete.get('byte_indices', [])
        
        # 计算删除的字节数
        deleted_bytes_count = len(byte_indices)
        
        # 删除字段
        del self.protocol_data[item_index]
        
        # 更新后续所有字段的字节索引
        for i in range(item_index, len(self.protocol_data)):
            field = self.protocol_data[i]
            old_indices = field.get('byte_indices', [])
            
            if old_indices:
                # 更新字节索引
                new_indices = [idx - deleted_bytes_count for idx in old_indices]
                field['byte_indices'] = new_indices
                
                # 更新字节索引起始位置
                if 'byte_index' in field:
                    field['byte_index'] = new_indices[0]
        
        # 刷新表格
        self.load_protocol_data()
        
        messagebox.showinfo("成功", f"已删除字段并更新了后续{len(self.protocol_data) - item_index}个字段的字节序号")
    
    def save_protocol(self):
        """保存协议数据到解析器"""
        # 更新解析器的协议结构
        self.protocol_parser.protocol_structure = self.protocol_data
        
        # 重建字节索引映射
        self.protocol_parser.byte_to_field_map = {}
        for field in self.protocol_data:
            for idx in field.get('byte_indices', [field.get('byte_index', 0)]):
                self.protocol_parser.byte_to_field_map[idx] = field
        
        # 调用保存回调
        if self.on_save_callback:
            self.on_save_callback()
        
        messagebox.showinfo("成功", "协议已保存")
    
    def import_csv(self):
        """导入CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择协议文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if file_path:
            if self.protocol_parser.load_from_csv(file_path):
                # 重新加载数据
                self.load_protocol_data()
                messagebox.showinfo("成功", "协议文件已导入")
            else:
                messagebox.showerror("错误", "无法导入协议文件")
    
    def export_csv(self):
        """导出CSV文件"""
        file_path = filedialog.asksaveasfilename(
            title="保存协议文件",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if file_path:
            # 先保存当前协议数据到解析器
            self.save_protocol()
            
            # 导出到CSV
            if self.protocol_parser.save_to_csv(file_path):
                messagebox.showinfo("成功", "协议文件已导出")
            else:
                messagebox.showerror("错误", "无法导出协议文件")
    
    def on_closing(self):
        """关闭窗口回调"""
        if messagebox.askokcancel("关闭", "是否保存当前协议?"):
            self.save_protocol()
        self.parent.destroy()

def main():
    # 设置中文支持
    try:
        if sys.platform.startswith('win'):
            locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass
    
    # 创建主窗口
    root = tk.Tk()
    root.title("协议管理器")
    root.geometry("800x600")
    
    # 创建协议管理器
    protocol_manager = ProtocolManager()
    
    # 直接创建协议编辑器作为主窗口
    editor = ProtocolEditor(root, protocol_manager.protocol_parser)
    
    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    main() 