
import serial
import time
import struct
import math
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import serial.tools.list_ports
import sys
import locale
import os
import collections
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 导入协议解析器和我们新的信号生成器
from protocol_parser import ProtocolParser
from signal_generator import SIGNAL_GENERATORS, SIGNAL_PARAM_DEFINITIONS, USER_SELECTABLE_SIGNALS

# --- 基本配置 ---
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
DEFAULT_PROTOCOL_FILE = "protocol_definition.csv"

protocol_parser = ProtocolParser()
if os.path.exists(DEFAULT_PROTOCOL_FILE):
    protocol_parser.load_from_csv(DEFAULT_PROTOCOL_FILE)
else:
    print(f"错误: 未找到默认协议定义文件 '{DEFAULT_PROTOCOL_FILE}'。")


# --- 主发送逻辑类 ---
class Protocol422Sender:
    def __init__(self, port='COM1', baudrate=19200, timeout=1, send_interval=0.005):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.send_interval = send_interval
        self.ser = None
        self.running = False
        self.frame_counter = 0
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.signal_params = {}
        self.command_fields = []
        self.simulation_time = 0.0

        # !! 核心修正：使用 reset_state 代替 is_resetting !!
        self.reset_state = "idle"  # "idle", "resetting", "zeroing"
        self.reset_start_time = 0.0
        self.last_values_before_reset = {}
        self.original_types_before_reset = {}

        self.update_protocol_structure()

    def update_protocol_structure(self):
        self.protocol_structure = protocol_parser.get_protocol_structure()
        self.command_fields = [f['name'] for f in self.protocol_structure if '指令' in f['name']]
        self._init_signal_params()
        print("发送端协议结构已更新。")

    def _init_signal_params(self):
        self.signal_params.clear()
        for field_name in self.command_fields:
            self.signal_params[field_name] = {'enabled': False, 'type': "不发送", 'params': {}}

    def initiate_reset(self):
        if self.reset_state != "idle":
            return False

        was_running = self.running
        if self.running:
            self.stop_sending()

        self.last_values_before_reset = self.get_current_signal_values(self.simulation_time)
        self.original_types_before_reset.clear()

        any_signal_to_reset = any(self.signal_params[name]['enabled'] for name in self.command_fields)

        if not any_signal_to_reset and not was_running:
            self.reset()
            return "simple_reset"

        if not any_signal_to_reset and was_running:
            self.finalize_reset()
            return "already_zero"

        self.reset_state = "resetting"
        for name in self.command_fields:
            if self.signal_params[name]['enabled']:
                self.original_types_before_reset[name] = self.signal_params[name]['type']
                self.signal_params[name]['type'] = "_平滑衰减"
                self.signal_params[name]['params'] = {"start_value": self.last_values_before_reset.get(name, 0)}

        self.reset_start_time = time.monotonic()
        self.start_sending()
        return "smooth_reset"

    def reset(self):
        self.simulation_time = 0.0
        self.frame_counter = 0
        print("发送端状态已复位。")

    def _check_reset_complete(self, t_reset):
        if self.reset_state != "resetting":
            return True
        if t_reset > 2.0:
            return True

        current_values = self.get_current_signal_values(self.simulation_time)
        return all(abs(current_values.get(name, 0)) < 1 for name in self.original_types_before_reset.keys())

    def finalize_reset(self):
        self.reset_state = "zeroing"
        for name, original_type in self.original_types_before_reset.items():
            if name in self.signal_params:
                self.signal_params[name]['type'] = original_type
                self.signal_params[name]['params'] = SIGNAL_PARAM_DEFINITIONS[original_type].copy()

    def connect(self):
        try:
            self.ser = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
            return True, f"已连接到 {self.port}"
        except serial.SerialException as e:
            return False, f"串口连接失败: {e}"

    def disconnect(self):
        if self.ser and self.ser.is_open:
            self.ser.close()
        self.running = False
        return True, "已断开串口连接"

    def create_frame(self, t):
        if not self.protocol_structure:
            return bytearray()

        last_field = self.protocol_structure[-1]
        total_bytes = last_field['byte_indices'][-1] + 1
        frame = bytearray(total_bytes)

        for field in self.protocol_structure:
            field_name, value = field['name'], 0
            if field_name == '帧头':
                struct.pack_into('<H', frame, 0, 0x55AA)
                continue

            if field_name in self.command_fields:
                config = self.signal_params.get(field_name)
                if self.reset_state == "resetting" and field_name in self.original_types_before_reset:
                    t_signal = time.monotonic() - self.reset_start_time
                    generator_func = SIGNAL_GENERATORS.get(config['type'])
                    if generator_func:
                        value = generator_func(config['params'], t_signal)
                elif self.reset_state == "zeroing" and config.get('enabled'):
                    value = 0
                elif self.reset_state == "idle" and config and config.get('enabled'):
                    generator_func = SIGNAL_GENERATORS.get(config['type'])
                    if generator_func:
                        value = generator_func(config['params'], t)
            elif field_name == '帧计数':
                value = self.frame_counter
                self.frame_counter = (self.frame_counter + 1) % 65536
            elif field_name == '校验和':
                continue

            start_idx, data_type = field['byte_indices'][0], field['type']
            try:
                value = int(value)
                if data_type == 'int16':
                    struct.pack_into('<h', frame, start_idx, value)
                elif data_type == 'uint16':
                    struct.pack_into('<H', frame, start_idx, value)
                elif data_type == 'int8':
                    struct.pack_into('<b', frame, start_idx, value)
                elif data_type == 'uint8':
                    struct.pack_into('<B', frame, start_idx, value)
            except (struct.error, ValueError):
                pass

        checksum_field = protocol_parser.get_field_by_name('校验和')
        if checksum_field:
            checksum_idx = checksum_field['byte_indices'][0]
            checksum = 0
            for i in range(2, checksum_idx):
                checksum ^= frame[i]
            frame[checksum_idx] = checksum

        return frame

    def send_frame(self, t):
        if not self.ser or not self.ser.is_open:
            return
        try:
            self.ser.write(self.create_frame(t))
        except serial.SerialException as e:
            print(f"串口写入错误: {e}")
            self.stop_sending()

    def start_sending(self):
        if self.running:
            return False, "已在发送中"
        if not self.ser or not self.ser.is_open:
            success, message = self.connect()
            if not success:
                return False, message

        self.running = True
        self.send_thread = threading.Thread(target=self._send_loop, daemon=True)
        self.send_thread.start()
        return True, "开始发送数据"

    def stop_sending(self):
        self.running = False
        if hasattr(self, 'send_thread') and threading.current_thread() != self.send_thread:
            self.send_thread.join(timeout=1.0)
        self.reset_state = "idle"
        return True, "停止发送数据"

    def _send_loop(self):
        while self.running:
            loop_start_time = time.monotonic()
            if self.reset_state == "resetting":
                t_reset = loop_start_time - self.reset_start_time
                self.send_frame(t_reset)
                if self._check_reset_complete(t_reset):
                    self.finalize_reset()
            else:
                self.send_frame(self.simulation_time)
                if self.reset_state == "idle":
                    self.simulation_time += self.send_interval

            elapsed = time.monotonic() - loop_start_time
            sleep_duration = self.send_interval - elapsed
            if sleep_duration > 0:
                time.sleep(sleep_duration)

    def get_current_signal_values(self, t):
        values = {}
        for field_name in self.command_fields:
            config = self.signal_params.get(field_name)
            if self.reset_state == "resetting" and field_name in self.original_types_before_reset:
                t_reset = time.monotonic() - self.reset_start_time
                values[field_name] = SIGNAL_GENERATORS["_平滑衰减"](config['params'], t_reset)
            elif self.reset_state == "zeroing" and config.get('enabled'):
                values[field_name] = 0
            elif self.reset_state == "idle" and config and config.get('enabled', False):
                generator_func = SIGNAL_GENERATORS.get(config['type'])
                if generator_func:
                    values[field_name] = generator_func(config['params'], t)
            else:
                values[field_name] = 0
        return values


# --- GUI部分 ---
class SenderGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯发送端 (可视化版)")
        self.master.geometry("1200x800")
        self.sender = Protocol422Sender()
        self.status_var = tk.StringVar(value="就绪")
        self.command_widgets = {}
        self.plot_data = {}
        self.plot_history_size = 100

        self.create_widgets()
        self.update_port_list()
        self._create_all_panels()

        self.master.after(50, self.update_plots)

    def create_widgets(self):
        paned_window = ttk.PanedWindow(self.master, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        left_panel = ttk.Frame(paned_window, width=280)
        paned_window.add(left_panel, weight=1)

        settings_frame = ttk.LabelFrame(left_panel, text="串口设置")
        settings_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(settings_frame, text="串口:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.port_combobox = ttk.Combobox(settings_frame, width=12, state="readonly")
        self.port_combobox.grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(settings_frame, text="刷新", command=self.update_port_list).grid(row=0, column=2, padx=5, pady=2)
        ttk.Label(settings_frame, text="波特率:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.baudrate_combobox = ttk.Combobox(settings_frame, values=[str(b) for b in BAUDRATES], width=12,
                                              state="readonly")
        self.baudrate_combobox.grid(row=1, column=1, padx=5, pady=2)
        self.baudrate_combobox.set("19200")
        ttk.Label(settings_frame, text="周期(s):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.send_interval_entry = ttk.Entry(settings_frame, width=15)
        self.send_interval_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=2)
        self.send_interval_entry.insert(0, "0.05")
        ttk.Button(settings_frame, text="应用串口设置", command=self.apply_serial_settings).grid(row=3, column=0,
                                                                                                 columnspan=3, pady=5)

        control_frame = ttk.LabelFrame(left_panel, text="主控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        self.start_button = ttk.Button(control_frame, text="开始", command=self.start_sender)
        self.start_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)
        self.stop_button = ttk.Button(control_frame, text="停止", command=self.stop_sender, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)
        self.reset_button = ttk.Button(control_frame, text="复位", command=self.reset_sender)
        self.reset_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)

        self.right_panel_container = ttk.Frame(paned_window)
        paned_window.add(self.right_panel_container, weight=4)

        status_bar = ttk.Frame(self.master, relief=tk.SUNKEN, padding="2 5")
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        ttk.Label(status_bar, textvariable=self.status_var).pack(side=tk.LEFT)

    def _create_all_panels(self):
        canvas = tk.Canvas(self.right_panel_container)
        scrollbar = ttk.Scrollbar(self.right_panel_container, orient="vertical", command=canvas.yview)
        content_frame = ttk.Frame(canvas)

        content_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=content_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        for field_name in self.sender.command_fields:
            if field_name not in self.plot_data:
                self.plot_data[field_name] = collections.deque(maxlen=self.plot_history_size)

            panel = ttk.LabelFrame(content_frame, text=field_name)
            ctrl_area = ttk.Frame(panel)

            enable_var = tk.BooleanVar(value=self.sender.signal_params[field_name]['enabled'])
            enable_check = ttk.Checkbutton(ctrl_area, text="启用", variable=enable_var,
                                           command=lambda name=field_name: self.apply_signal_params(name))

            signal_type_combo = ttk.Combobox(ctrl_area, values=USER_SELECTABLE_SIGNALS, width=10, state="readonly")
            signal_type_combo.set(self.sender.signal_params[field_name]['type'])
            signal_type_combo.bind("<<ComboboxSelected>>", lambda e, n=field_name: self.on_signal_type_change(n))

            param_frame = ttk.Frame(ctrl_area)
            apply_btn = ttk.Button(ctrl_area, text="应用", command=lambda n=field_name: self.apply_signal_params(n))

            fig, ax = plt.subplots(figsize=(5, 2), dpi=100)
            fig.tight_layout(pad=2.5)
            canvas_widget = FigureCanvasTkAgg(fig, master=panel)

            self.command_widgets[field_name] = {'panel': panel, 'ctrl_area': ctrl_area, 'enable_var': enable_var,
                                                'enable_check': enable_check, 'combo': signal_type_combo,
                                                'param_frame': param_frame, 'apply_btn': apply_btn, 'param_vars': {},
                                                'fig': fig, 'ax': ax, 'canvas': canvas_widget}
            self._create_param_entries(field_name)

            ctrl_area.pack(fill=tk.X, padx=5, pady=2)
            enable_check.pack(side=tk.LEFT, padx=(0, 10))
            ttk.Label(ctrl_area, text="信号:").pack(side=tk.LEFT, padx=(0, 5))
            signal_type_combo.pack(side=tk.LEFT)
            param_frame.pack(side=tk.LEFT, fill=tk.X, padx=5)
            apply_btn.pack(side=tk.LEFT, padx=5)
            canvas_widget.get_tk_widget().pack(fill=tk.X, expand=True)

            panel.pack(fill=tk.X, padx=5, pady=5, expand=True)

    def _create_param_entries(self, field_name):
        widgets = self.command_widgets[field_name]
        param_frame = widgets['param_frame']
        widgets['param_vars'].clear()

        for widget in param_frame.winfo_children():
            widget.destroy()

        signal_type = widgets['combo'].get()
        param_defs = SIGNAL_PARAM_DEFINITIONS.get(signal_type, {})
        current_params = self.sender.signal_params[field_name]['params']

        for param_name, default_value in param_defs.items():
            ttk.Label(param_frame, text=f"{param_name}:").pack(side=tk.LEFT, padx=(5, 2))
            var = tk.StringVar(value=str(current_params.get(param_name, default_value)))
            entry = ttk.Entry(param_frame, textvariable=var, width=8)
            entry.pack(side=tk.LEFT)
            widgets['param_vars'][param_name] = var

    def on_signal_type_change(self, field_name):
        widgets = self.command_widgets[field_name]
        new_type = widgets['combo'].get()
        self.sender.signal_params[field_name]['type'] = new_type
        self.sender.signal_params[field_name]['params'] = SIGNAL_PARAM_DEFINITIONS[new_type].copy()
        self._create_param_entries(field_name)
        self.apply_signal_params(field_name)

    def apply_serial_settings(self):
        try:
            interval = float(self.send_interval_entry.get())
            self.sender.send_interval = interval if interval > 0 else 0.001
            self.sender.baudrate = int(self.baudrate_combobox.get())
            self.status_var.set("串口设置已应用")
            return True
        except Exception as e:
            messagebox.showerror("输入错误", f"请输入有效的数值: {e}")
            return False

    def apply_signal_params(self, field_name):
        try:
            widgets = self.command_widgets[field_name]
            self.sender.signal_params[field_name]['enabled'] = widgets['enable_var'].get()
            params = self.sender.signal_params[field_name]['params']
            for param_name, var in widgets['param_vars'].items():
                params[param_name] = float(var.get())
            self.status_var.set(f"'{field_name}' 参数已应用")
            return True
        except Exception as e:
            messagebox.showerror("输入错误", f"'{field_name}' 的参数无效: {e}")
            return False

    def update_plots(self):
        current_values = self.sender.get_current_signal_values(self.sender.simulation_time )

        if self.sender.running:
            for name, value in current_values.items():
                if name in self.plot_data:
                    self.plot_data[name].append(value)

        for name, value in current_values.items():
            config = self.sender.signal_params.get(name)

            # !! 核心修正：使用 reset_state 判断绘图条件 !!
            if config and (config.get('enabled') or (
                    self.sender.reset_state == "resetting" and name in self.sender.original_types_before_reset)):
                widgets = self.command_widgets.get(name)
                if widgets and widgets['panel'].winfo_exists():
                    ax = widgets['ax']
                    ax.clear()
                    ax.plot(list(self.plot_data[name]))
                    ax.set_title(f"值: {value:.2f}", fontsize=9)
                    ax.grid(True)
                    widgets['canvas'].draw()

        self.master.after(50, self.update_plots)

    def start_sender(self):
        if not self.apply_serial_settings(): return
        for field_name in self.sender.command_fields:
            if not self.apply_signal_params(field_name): return

        port = self.port_combobox.get()
        if not port or "无可用串口" in port:
            messagebox.showerror("错误", "请选择一个有效的串口。")
            return

        self.sender.port = port
        success, message = self.sender.start_sending()
        if success:
            self.status_var.set("正在发送...")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.reset_button.config(state=tk.NORMAL)
        else:
            messagebox.showerror("启动错误", message)

    def stop_sender(self):
        self.sender.stop_sending()
        self.status_var.set("已停止发送")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.reset_button.config(state=tk.NORMAL)

        for name, widgets in self.command_widgets.items():
            # 恢复UI到复位前的状态
            if 'original_type' in self.sender.signal_params[name]:
                widgets['combo'].set(self.sender.signal_params[name]['original_type'])
                self._create_param_entries(name)
        self.sender.original_types_before_reset.clear()

    def reset_sender(self):
        if self.sender.reset_state != "idle":
            return

        reset_action = self.sender.initiate_reset()

        if reset_action == "smooth_reset":
            self.status_var.set("正在平滑复位...")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)
            self.reset_button.config(state=tk.DISABLED)
            self.master.after(100, self._check_reset_gui_finalize)
        elif reset_action == "simple_reset":
            self.status_var.set("已复位 (无活动信号)")
            for name in self.plot_data:
                self.plot_data[name].clear()
            self.update_plots()
        elif reset_action == "already_zero":
            self.status_var.set("已在零位，停止发送")
            self.stop_sender()

    def _check_reset_gui_finalize(self):
        if self.sender.reset_state == "zeroing":
            self.status_var.set("归零完成，持续发送0。请手动停止。")
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.reset_button.config(state=tk.DISABLED)
        elif self.sender.reset_state == "resetting":
            self.master.after(100, self._check_reset_gui_finalize)
        # 如果状态变回 idle (例如，如果复位期间出错停止了)，也恢复UI
        else:
            self.stop_sender()

    def update_port_list(self):
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combobox['values'] = ports if ports else ["无可用串口"]
        if ports:
            self.port_combobox.current(0)

    def on_closing(self):
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            for widgets in self.command_widgets.values():
                plt.close(widgets['fig'])
            self.sender.stop_sending()
            self.sender.disconnect()
            self.master.destroy()


def main():
    root = tk.Tk()
    app = SenderGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()

