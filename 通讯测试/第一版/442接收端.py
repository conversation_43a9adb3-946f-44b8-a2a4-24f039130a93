import serial
import struct
import time
import threading
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox
import queue
import serial.tools.list_ports
import sys
import locale

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 协议数据结构定义
PROTOCOL_STRUCTURE = [
    {'name': '帧头', 'byte_index': 0, 'length': 1, 'type': 'hex', 'description': '固定值0x7E'},
    {'name': 'ID', 'byte_index': 1, 'length': 1, 'type': 'hex', 'description': '固定值0x01'},
    {'name': '伺服机构位置指令', 'byte_index': 2, 'length': 2, 'type': 'int16', 'description': '16位整型'},
    {'name': '隔离阀1开关信号', 'byte_index': 4, 'length': 1, 'type': 'uint8', 'description': '0:非隔离状态，1:隔离状态'},
    {'name': '隔离阀2开关信号', 'byte_index': 5, 'length': 1, 'type': 'uint8', 'description': '0:非隔离状态，1:隔离状态'},
    {'name': '零位锁开关指令', 'byte_index': 6, 'length': 1, 'type': 'uint8', 'description': '0:闭锁，1:开启'},
    {'name': '预留字节', 'byte_index': 7, 'length': 1, 'type': 'uint8', 'description': '预留'},
    {'name': '帧计数', 'byte_index': 8, 'length': 2, 'type': 'uint16', 'description': '无符号16位整型'},
    {'name': '校验和', 'byte_index': 10, 'length': 1, 'type': 'uint8', 'description': '不包括帧头的异或和'}
]

class Protocol422Receiver:
    def __init__(self, port=None, baudrate=460800, timeout=1):
        """初始化422通讯接收端"""
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        self.running = False
        self.data_queue = queue.Queue(maxsize=1000)
        self.frame_header = 0x7E
        self.frame_id = 0x01
        
        # 数据存储
        self.data_history = {
            'timestamp': [],
            'servo_position': [],
            'isolation_valve1': [],
            'isolation_valve2': [],
            'zero_lock': [],
            'frame_counter': []
        }
        
        # 原始数据帧存储
        self.raw_frames = []
        self.max_raw_frames = 100  # 最多存储100帧原始数据
        
        # 数据缓冲区
        self.buffer = bytearray()
        
        # 最大历史记录点数
        self.max_history = 100
    
    def connect(self, port):
        """连接到串口"""
        try:
            # 如果之前有连接，先关闭
            if self.ser and self.ser.is_open:
                self.ser.close()
                time.sleep(0.5)  # 等待串口释放
            
            self.port = port
            # 尝试以独占方式打开串口
            self.ser = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                exclusive=True  # 尝试独占方式打开
            )
            return True, "连接成功"
        except serial.SerialException as e:
            error_msg = str(e)
            # 处理常见错误
            if "PermissionError" in error_msg:
                return False, f"串口访问被拒绝: {port}。请检查是否有其他程序正在使用该串口，或尝试以管理员身份运行。"
            elif "FileNotFoundError" in error_msg:
                return False, f"找不到串口: {port}。请检查串口名称是否正确。"
            else:
                return False, f"串口错误: {error_msg}"
    
    def start(self):
        """启动接收线程"""
        if not self.running and self.ser and self.ser.is_open:
            self.running = True
            self.receive_thread = threading.Thread(target=self._receive_loop)
            self.receive_thread.daemon = True
            self.receive_thread.start()
            return True, "接收线程已启动"
        elif not self.ser:
            return False, "串口未连接"
        elif not self.ser.is_open:
            return False, "串口未打开"
        else:
            return False, "接收线程已在运行"
    
    def stop(self):
        """停止接收线程"""
        self.running = False
        if hasattr(self, 'receive_thread'):
            self.receive_thread.join(timeout=1.0)
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已关闭")
    
    def _receive_loop(self):
        """接收数据循环"""
        while self.running:
            try:
                # 确保串口已连接
                if not self.ser or not self.ser.is_open:
                    print("串口未连接或已关闭")
                    self.running = False
                    break
                
                # 读取可用数据
                if self.ser.in_waiting > 0:
                    data = self.ser.read(self.ser.in_waiting)
                    self.buffer.extend(data)
                    
                    # 处理缓冲区中的数据
                    self._process_buffer()
                    
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.001)
                
            except serial.SerialException as e:
                print(f"串口错误: {e}")
                self.running = False
                break
    
    def _process_buffer(self):
        """处理接收缓冲区"""
        # 寻找帧头
        while len(self.buffer) >= 11:  # 至少需要完整的帧长度
            # 查找帧头
            if self.buffer[0] != self.frame_header:
                self.buffer.pop(0)
                continue
            
            # 检查ID
            if self.buffer[1] != self.frame_id:
                self.buffer.pop(0)
                continue
            
            # 提取数据
            try:
                # 保存原始数据帧
                raw_frame = bytes(self.buffer[:11])
                
                # 伺服机构位置指令 (2字节)
                servo_position = struct.unpack('<h', self.buffer[2:4])[0]
                
                # 隔离阀1开关信号 (1字节)
                isolation_valve1 = self.buffer[4]
                
                # 隔离阀2开关信号 (1字节)
                isolation_valve2 = self.buffer[5]
                
                # 零位锁开关指令 (1字节)
                zero_lock = self.buffer[6]
                
                # 预留字节 (1字节)
                reserved = self.buffer[7]
                
                # 帧计数 (2字节)
                frame_counter = struct.unpack('<H', self.buffer[8:10])[0]
                
                # 校验和 (1字节)
                checksum = self.buffer[10]
                
                # 验证校验和
                calculated_checksum = 0
                for i in range(1, 10):
                    calculated_checksum ^= self.buffer[i]
                
                if calculated_checksum == checksum:
                    # 校验通过，处理数据
                    timestamp = time.time()
                    
                    # 存储原始数据帧
                    self.raw_frames.append(raw_frame)
                    if len(self.raw_frames) > self.max_raw_frames:
                        self.raw_frames.pop(0)  # 移除最旧的帧
                    
                    # 存储数据
                    self.data_history['timestamp'].append(timestamp)
                    self.data_history['servo_position'].append(servo_position)
                    self.data_history['isolation_valve1'].append(isolation_valve1)
                    self.data_history['isolation_valve2'].append(isolation_valve2)
                    self.data_history['zero_lock'].append(zero_lock)
                    self.data_history['frame_counter'].append(frame_counter)
                    
                    # 限制历史记录长度
                    if len(self.data_history['timestamp']) > self.max_history:
                        for key in self.data_history:
                            self.data_history[key] = self.data_history[key][-self.max_history:]
                    
                    # 将数据放入队列供GUI使用
                    data_packet = {
                        'timestamp': timestamp,
                        'raw_frame': raw_frame,
                        'servo_position': servo_position,
                        'isolation_valve1': isolation_valve1,
                        'isolation_valve2': isolation_valve2,
                        'zero_lock': zero_lock,
                        'frame_counter': frame_counter
                    }
                    
                    try:
                        self.data_queue.put_nowait(data_packet)
                    except queue.Full:
                        # 队列满，移除最旧的数据
                        try:
                            self.data_queue.get_nowait()
                            self.data_queue.put_nowait(data_packet)
                        except:
                            pass
                    
                    print(f"接收到数据: 位置={servo_position}, 隔离阀1={isolation_valve1}, "
                          f"隔离阀2={isolation_valve2}, 零位锁={zero_lock}, 帧计数={frame_counter}")
                else:
                    print(f"校验和错误: 计算值={calculated_checksum}, 接收值={checksum}")
                
                # 移除已处理的数据
                self.buffer = self.buffer[11:]
                
            except Exception as e:
                print(f"数据解析错误: {e}")
                self.buffer.pop(0)
    
    def get_latest_data(self):
        """获取最新数据"""
        try:
            return self.data_queue.get_nowait()
        except queue.Empty:
            return None
    
    def get_raw_frames(self):
        """获取原始数据帧"""
        return self.raw_frames

class ReceiverGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯接收端可视化")
        self.master.geometry("1000x800")
        
        # 创建接收端实例，但不立即连接串口
        self.receiver = Protocol422Receiver()
        
        # 状态变量
        self.status_var = tk.StringVar(value="就绪")
        
        # 选择显示的字节
        self.selected_byte_var = tk.StringVar(value="伺服机构位置指令")
        
        # 创建GUI组件
        self.create_widgets()
        
        # 数据存储
        self.data_history = {
            'timestamp': [],
            'raw_frames': [],
            'servo_position': [],
            'isolation_valve1': [],
            'isolation_valve2': [],
            'zero_lock': [],
            'frame_counter': [],
            'selected_byte_values': []  # 存储选择的字节值
        }
        
        # 最大历史记录点数
        self.max_history = 100
        
        # 启动定时器更新GUI
        self.master.after(100, self.update_gui)
        
        # 获取可用串口列表
        self.update_port_list()
    
    def update_port_list(self):
        """更新可用串口列表"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combobox['values'] = ports
            self.port_combobox.current(0)  # 选择第一个串口
        else:
            self.port_combobox['values'] = ["无可用串口"]
            self.port_combobox.current(0)
            self.status_var.set("未检测到可用串口")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建控制框架
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)
        
        # 串口设置
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=5)
        self.port_combobox = ttk.Combobox(control_frame, width=10)
        self.port_combobox.pack(side=tk.LEFT, padx=5)
        
        # 刷新串口按钮
        self.refresh_button = ttk.Button(control_frame, text="刷新", command=self.update_port_list)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 启动/停止按钮
        self.start_button = ttk.Button(control_frame, text="启动接收", command=self.start_receiver)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止接收", command=self.stop_receiver)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 清除数据按钮
        self.clear_button = ttk.Button(control_frame, text="清除数据", command=self.clear_data)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        ttk.Label(control_frame, text="状态:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        # 创建数据选择框架
        data_select_frame = ttk.Frame(main_frame, padding="5")
        data_select_frame.pack(fill=tk.X, pady=5)
        
        # 字节选择下拉框
        ttk.Label(data_select_frame, text="选择显示数据:").pack(side=tk.LEFT, padx=5)
        byte_names = [item['name'] for item in PROTOCOL_STRUCTURE]
        self.byte_select_combobox = ttk.Combobox(data_select_frame, textvariable=self.selected_byte_var, values=byte_names, width=20)
        self.byte_select_combobox.pack(side=tk.LEFT, padx=5)
        self.byte_select_combobox.current(2)  # 默认选择伺服机构位置指令
        self.byte_select_combobox.bind("<<ComboboxSelected>>", self.on_byte_selected)
        
        # 字节描述标签
        ttk.Label(data_select_frame, text="描述:").pack(side=tk.LEFT, padx=(20, 5))
        self.byte_description_var = tk.StringVar(value=PROTOCOL_STRUCTURE[2]['description'])
        ttk.Label(data_select_frame, textvariable=self.byte_description_var).pack(side=tk.LEFT)
        
        # 创建图表框架
        chart_frame = ttk.Frame(main_frame, padding="5")
        chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建图表
        self.fig, self.axes = plt.subplots(2, 1, figsize=(10, 8), dpi=100)
        
        # 选择的数据图表
        self.selected_data_line, = self.axes[0].plot([], [], 'b-', label='选择的数据')
        self.axes[0].set_title('选择的数据')
        self.axes[0].set_ylabel('值')
        self.axes[0].grid(True)
        self.axes[0].legend(loc='upper right')
        
        # 状态信号图表
        self.valve1_line, = self.axes[1].plot([], [], 'r-', label='隔离阀1')
        self.valve2_line, = self.axes[1].plot([], [], 'g-', label='隔离阀2')
        self.lock_line, = self.axes[1].plot([], [], 'c-', label='零位锁')
        self.axes[1].set_title('状态信号')
        self.axes[1].set_xlabel('时间 (秒)')
        self.axes[1].set_ylabel('状态')
        self.axes[1].set_ylim(-0.1, 1.1)
        self.axes[1].grid(True)
        self.axes[1].legend(loc='upper right')
        
        # 调整布局
        plt.tight_layout()
        
        # 将图表添加到tkinter窗口
        self.canvas = FigureCanvasTkAgg(self.fig, master=chart_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建数据显示框架
        data_frame = ttk.LabelFrame(main_frame, text="实时数据", padding="5")
        data_frame.pack(fill=tk.X, pady=5)
        
        # 数据标签
        self.data_labels = {}
        data_items = [
            ('selected_byte', '选择的数据:'),
            ('servo_position', '伺服位置:'),
            ('isolation_valve1', '隔离阀1:'),
            ('isolation_valve2', '隔离阀2:'),
            ('zero_lock', '零位锁:'),
            ('frame_counter', '帧计数:')
        ]
        
        for i, (key, text) in enumerate(data_items):
            frame = ttk.Frame(data_frame)
            frame.grid(row=i//3, column=i%3, padx=10, pady=5, sticky=tk.W)
            
            ttk.Label(frame, text=text).pack(side=tk.LEFT)
            self.data_labels[key] = ttk.Label(frame, text="--")
            self.data_labels[key].pack(side=tk.LEFT)
        
        # 创建原始数据帧显示框架
        raw_frame_frame = ttk.LabelFrame(main_frame, text="原始数据帧 (十六进制)", padding="5")
        raw_frame_frame.pack(fill=tk.X, pady=5)
        
        # 原始数据帧文本框
        self.raw_frame_text = tk.Text(raw_frame_frame, height=3, wrap=tk.WORD)
        self.raw_frame_text.pack(fill=tk.X)
        self.raw_frame_text.config(state=tk.DISABLED)  # 只读
    
    def on_byte_selected(self, event=None):
        """当选择不同的字节时更新描述和图表"""
        selected_name = self.selected_byte_var.get()
        for item in PROTOCOL_STRUCTURE:
            if item['name'] == selected_name:
                self.byte_description_var.set(item['description'])
                # 更新图表标题
                self.axes[0].set_title(f"{selected_name} - {item['description']}")
                
                # 根据数据类型设置Y轴范围
                if item['type'] == 'uint8':
                    self.axes[0].set_ylim(-10, 265)  # 0-255 with some margin
                elif item['type'] == 'int16':
                    self.axes[0].set_ylim(-35000, 35000)  # -32768 to 32767 with some margin
                elif item['type'] == 'uint16':
                    self.axes[0].set_ylim(-1000, 66000)  # 0-65535 with some margin
                elif item['type'] == 'hex':
                    self.axes[0].set_ylim(-10, 265)  # 0-255 with some margin
                
                self.canvas.draw()
                break
    
    def extract_selected_byte_value(self, raw_frame):
        """从原始数据帧中提取选择的字节值"""
        selected_name = self.selected_byte_var.get()
        for item in PROTOCOL_STRUCTURE:
            if item['name'] == selected_name:
                start_idx = item['byte_index']
                length = item['length']
                data_type = item['type']
                
                # 提取字节
                byte_data = raw_frame[start_idx:start_idx+length]
                
                # 根据数据类型解析
                if data_type == 'uint8':
                    if length == 1:
                        return byte_data[0]
                    else:
                        return int.from_bytes(byte_data, byteorder='little', signed=False)
                elif data_type == 'int16':
                    return struct.unpack('<h', byte_data)[0]
                elif data_type == 'uint16':
                    return struct.unpack('<H', byte_data)[0]
                elif data_type == 'hex':
                    if length == 1:
                        return byte_data[0]
                    else:
                        return int.from_bytes(byte_data, byteorder='little', signed=False)
                
                return None
        return None
    
    def start_receiver(self):
        """启动接收器"""
        port = self.port_combobox.get()
        if port == "无可用串口":
            self.status_var.set("错误: 无可用串口")
            messagebox.showerror("错误", "未检测到可用串口")
            return
        
        # 连接串口
        success, message = self.receiver.connect(port)
        if not success:
            self.status_var.set(message)
            messagebox.showerror("串口连接错误", message)
            return
        
        # 启动接收线程
        success, message = self.receiver.start()
        if success:
            self.status_var.set(message)
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
        else:
            self.status_var.set(message)
            messagebox.showerror("启动错误", message)
    
    def stop_receiver(self):
        """停止接收器"""
        if hasattr(self, 'receiver'):
            self.receiver.stop()
        
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("已停止接收")
    
    def clear_data(self):
        """清除数据"""
        for key in self.data_history:
            self.data_history[key] = []
        
        # 清除图表
        self.selected_data_line.set_data([], [])
        self.valve1_line.set_data([], [])
        self.valve2_line.set_data([], [])
        self.lock_line.set_data([], [])
        self.canvas.draw()
        
        # 清除数据标签
        for key in self.data_labels:
            self.data_labels[key].config(text="--")
        
        # 清除原始数据帧显示
        self.raw_frame_text.config(state=tk.NORMAL)
        self.raw_frame_text.delete(1.0, tk.END)
        self.raw_frame_text.config(state=tk.DISABLED)
        
        self.status_var.set("数据已清除")
    
    def update_gui(self):
        """更新GUI"""
        if hasattr(self, 'receiver') and self.receiver.running:
            # 获取最新数据
            data = self.receiver.get_latest_data()
            if data:
                # 提取选择的字节值
                selected_byte_value = self.extract_selected_byte_value(data['raw_frame'])
                
                # 更新数据历史
                self.data_history['timestamp'].append(data['timestamp'])
                self.data_history['raw_frames'].append(data['raw_frame'])
                self.data_history['servo_position'].append(data['servo_position'])
                self.data_history['isolation_valve1'].append(data['isolation_valve1'])
                self.data_history['isolation_valve2'].append(data['isolation_valve2'])
                self.data_history['zero_lock'].append(data['zero_lock'])
                self.data_history['frame_counter'].append(data['frame_counter'])
                self.data_history['selected_byte_values'].append(selected_byte_value)
                
                # 限制历史记录长度
                if len(self.data_history['timestamp']) > self.max_history:
                    for key in self.data_history:
                        self.data_history[key] = self.data_history[key][-self.max_history:]
                
                # 更新数据标签
                self.data_labels['selected_byte'].config(text=str(selected_byte_value))
                self.data_labels['servo_position'].config(text=str(data['servo_position']))
                self.data_labels['isolation_valve1'].config(text=str(data['isolation_valve1']))
                self.data_labels['isolation_valve2'].config(text=str(data['isolation_valve2']))
                self.data_labels['zero_lock'].config(text=str(data['zero_lock']))
                self.data_labels['frame_counter'].config(text=str(data['frame_counter']))
                
                # 更新原始数据帧显示
                hex_str = ' '.join([f"{b:02X}" for b in data['raw_frame']])
                self.raw_frame_text.config(state=tk.NORMAL)
                self.raw_frame_text.delete(1.0, tk.END)
                self.raw_frame_text.insert(tk.END, hex_str)
                self.raw_frame_text.config(state=tk.DISABLED)
                
                # 更新图表
                # 使用相对时间作为X轴
                if self.data_history['timestamp']:
                    start_time = self.data_history['timestamp'][0]
                    times = [t - start_time for t in self.data_history['timestamp']]
                    
                    self.selected_data_line.set_data(times, self.data_history['selected_byte_values'])
                    self.valve1_line.set_data(times, self.data_history['isolation_valve1'])
                    self.valve2_line.set_data(times, self.data_history['isolation_valve2'])
                    self.lock_line.set_data(times, self.data_history['zero_lock'])
                    
                    # 自动调整X轴范围
                    for ax in self.axes:
                        ax.relim()
                        ax.autoscale_view(scalex=True, scaley=False)
                    
                    self.canvas.draw()
        
        # 继续定时更新
        self.master.after(100, self.update_gui)
    
    def on_closing(self):
        """窗口关闭时的处理"""
        if hasattr(self, 'receiver'):
            self.receiver.stop()
        self.master.destroy()

def main():
    # 设置中文支持
    try:
        # 尝试设置系统默认编码
        if sys.platform.startswith('win'):
            locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass
    
    root = tk.Tk()
    app = ReceiverGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
