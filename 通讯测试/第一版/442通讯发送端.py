import serial
import time
import struct
import numpy as np
import math
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import serial.tools.list_ports
import sys
import locale

# 设置中文显示
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'chs')
    except:
        pass

class Protocol422Sender:
    def __init__(self, port='COM1', baudrate=460800, timeout=1):
        """初始化422通讯发送端"""
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = None
        self.running = False
        self.counter = 0  # 帧计数器
        
        # 帧头和ID固定值
        self.FRAME_HEADER = 0x7E
        self.FRAME_ID = 0x01
    
    def connect(self):
        """连接到串口"""
        try:
            # 如果之前有连接，先关闭
            if self.ser and self.ser.is_open:
                self.ser.close()
                time.sleep(0.5)  # 等待串口释放
                
            self.ser = serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
            return True, "连接成功"
        except serial.SerialException as e:
            error_msg = str(e)
            # 处理常见错误
            if "PermissionError" in error_msg:
                return False, f"串口访问被拒绝: {self.port}。请检查是否有其他程序正在使用该串口，或尝试以管理员身份运行。"
            elif "FileNotFoundError" in error_msg:
                return False, f"找不到串口: {self.port}。请检查串口名称是否正确。"
            else:
                return False, f"串口错误: {error_msg}"
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            return True, "串口已关闭"
        return False, "串口未连接"
        
    def generate_sin_value(self, amplitude, frequency, phase, t):
        """生成正弦波信号"""
        return amplitude * math.sin(2 * math.pi * frequency * t + phase)
    
    def create_frame(self):
        """创建一个数据帧"""
        t = time.time()  # 当前时间，用于生成不同的正弦值
        
        # 创建数据帧
        frame = bytearray()
        
        # 帧头 (1字节)
        frame.append(self.FRAME_HEADER)  # 0x7E
        
        # ID (1字节)
        frame.append(self.FRAME_ID)  # 0x01
        
        # 伺服机构位置指令 (2字节, 16位整型)
        servo_position = int(self.generate_sin_value(32767, 0.1, 0, t))
        frame.extend(struct.pack('<h', servo_position))
        
        # 隔离阀1开关信号 (1字节)
        isolation_valve1 = 1 if self.generate_sin_value(1, 0.05, 0, t) > 0 else 0
        frame.append(isolation_valve1)
        
        # 隔离阀2开关信号 (1字节)
        isolation_valve2 = 1 if self.generate_sin_value(1, 0.03, math.pi/4, t) > 0 else 0
        frame.append(isolation_valve2)
        
        # 零位锁开关指令 (1字节)
        zero_lock = 1 if self.generate_sin_value(1, 0.02, math.pi/2, t) > 0 else 0
        frame.append(zero_lock)
        
        # 预留一个字节 (1字节)
        frame.append(0)
        
        # 帧计数 (2字节)
        frame.extend(struct.pack('<H', self.counter))
        self.counter = (self.counter + 1) % 65536  # 循环计数
        
        # 校验和 (1字节): 不包括帧头的异或和
        checksum = 0
        for i in range(1, len(frame)):
            checksum ^= frame[i]
        frame.append(checksum)
        
        return frame
    
    def send_frame(self):
        """发送一个数据帧"""
        if not self.ser or not self.ser.is_open:
            return None, "串口未连接"
            
        frame = self.create_frame()
        try:
            self.ser.write(frame)
            return frame, f"发送数据帧: {' '.join([f'{b:02X}' for b in frame])}"
        except serial.SerialException as e:
            return None, f"发送错误: {e}"
    
    def start_sending(self):
        """开始持续发送数据"""
        if self.running:
            return False, "已经在发送中"
        
        if not self.ser or not self.ser.is_open:
            success, message = self.connect()
            if not success:
                return False, message
        
        self.running = True
        self.send_thread = threading.Thread(target=self._send_loop)
        self.send_thread.daemon = True
        self.send_thread.start()
        return True, "开始发送数据"
    
    def stop_sending(self):
        """停止发送数据"""
        self.running = False
        if hasattr(self, 'send_thread'):
            self.send_thread.join(timeout=1.0)
        return True, "停止发送数据"
    
    def _send_loop(self):
        """发送循环"""
        while self.running:
            frame, message = self.send_frame()
            print(message)
            
            # 按照协议要求的发送周期5ms
            time.sleep(0.005)

def simulate_receiver_data():
    """模拟接收端数据，生成与下位机422通讯协议对应的数据"""
    t = time.time()
    data = {}
    
    # 生成各种模拟数据
    # 位置反馈
    data['position_feedback'] = int(math.sin(t * 0.5) * 32767)
    
    # 位置反馈1通道结果
    data['position_feedback_ch1'] = int(math.sin(t * 0.6) * 32767)
    
    # 位置反馈2通道结果
    data['position_feedback_ch2'] = int(math.sin(t * 0.7) * 32767)
    
    # 电机泵变频反馈
    data['motor_pump1_freq'] = int(math.sin(t * 0.3) * 100 + 100)  # 0-200
    data['motor_pump2_freq'] = int(math.sin(t * 0.4) * 100 + 100)  # 0-200
    
    # 电机泵转速指令
    data['motor_pump1_speed_cmd'] = int(math.sin(t * 0.2) * 1000 + 1500)  # 500-2500 rpm
    data['motor_pump2_speed_cmd'] = int(math.sin(t * 0.25) * 1000 + 1500)  # 500-2500 rpm
    
    # 电机泵转速反馈
    data['motor_pump1_speed_fb'] = int(math.sin(t * 0.22) * 1000 + 1500)  # 500-2500 rpm
    data['motor_pump2_speed_fb'] = int(math.sin(t * 0.27) * 1000 + 1500)  # 500-2500 rpm
    
    # 电机泵轴电流指令
    data['motor_pump1q_current_cmd'] = int(math.sin(t * 0.15) * 10 + 10)  # 0-20 A
    
    # 电机泵轴电流反馈
    data['motor_pump1q_current_fb'] = int(math.sin(t * 0.17) * 10 + 10)  # 0-20 A
    data['motor_pump2q_current_cmd'] = int(math.sin(t * 0.18) * 10 + 10)  # 0-20 A
    data['motor_pump2q_current_fb'] = int(math.sin(t * 0.19) * 10 + 10)  # 0-20 A
    
    # 8位整型数据
    data['motor_pump1d_current_fb'] = int(math.sin(t * 0.11) * 127)  # -127 to 127
    data['motor_pump2d_current_fb'] = int(math.sin(t * 0.12) * 127)  # -127 to 127
    data['motor_pump1A_current'] = int(math.sin(t * 0.13) * 127)  # 0-255
    data['motor_pump1B_current'] = int(math.sin(t * 0.14) * 127)  # 0-255
    data['motor_pump2A_current'] = int(math.sin(t * 0.15) * 127)  # 0-255
    data['motor_pump2B_current'] = int(math.sin(t * 0.16) * 127)  # 0-255
    
    # 电压数据
    data['main_voltage'] = int(math.sin(t * 0.05) * 10 + 24)  # 14-34 V
    data['main_current'] = int(math.sin(t * 0.06) * 20 + 20)  # 0-40 A
    
    # 压力数据 (MPa)
    data['pressure_A'] = math.sin(t * 0.07) * 10 + 10  # 0-20 MPa
    data['pressure_B'] = math.sin(t * 0.08) * 10 + 10  # 0-20 MPa
    data['pressure_return'] = math.sin(t * 0.09) * 5 + 5  # 0-10 MPa
    data['pressure_pump1A'] = math.sin(t * 0.10) * 15 + 15  # 0-30 MPa
    data['pressure_pump1B'] = math.sin(t * 0.11) * 15 + 15  # 0-30 MPa
    data['pressure_pump2A'] = math.sin(t * 0.12) * 15 + 15  # 0-30 MPa
    data['pressure_pump2B'] = math.sin(t * 0.13) * 15 + 15  # 0-30 MPa
    
    # 温度数据 (°C)
    data['temp_A'] = math.sin(t * 0.02) * 40 + 50  # 10-90 °C
    data['temp_B'] = math.sin(t * 0.03) * 40 + 50  # 10-90 °C
    data['temp_return'] = math.sin(t * 0.04) * 40 + 50  # 10-90 °C
    
    # 电磁阀状态
    data['solenoid_status'] = int(0b1010101)  # 随机电磁阀状态
    
    # 电源电压
    data['voltage_1.1V'] = 1.1 + math.sin(t * 0.01) * 0.05  # 约1.1V
    data['voltage_1.9V'] = 1.9 + math.sin(t * 0.01) * 0.05  # 约1.9V
    data['voltage_3.3V'] = 3.3 + math.sin(t * 0.01) * 0.1   # 约3.3V
    data['voltage_5V'] = 5.0 + math.sin(t * 0.01) * 0.2     # 约5V
    data['voltage_10V'] = 10.0 + math.sin(t * 0.01) * 0.3   # 约10V
    data['voltage_-10V'] = -10.0 + math.sin(t * 0.01) * 0.3 # 约-10V
    data['voltage_15V'] = 15.0 + math.sin(t * 0.01) * 0.5   # 约15V
    data['voltage_-15V'] = -15.0 + math.sin(t * 0.01) * 0.5 # 约-15V
    data['voltage_28V'] = 28.0 + math.sin(t * 0.01) * 1.0   # 约28V
    
    # 虚拟余度计算结果
    data['position_redundancy'] = int(math.sin(t * 0.14) * 127)  # 冗余位置
    data['speed_pump1_redundancy'] = int(math.sin(t * 0.15) * 1000 + 1500)  # 冗余转速
    data['speed_pump2_redundancy'] = int(math.sin(t * 0.16) * 1000 + 1500)  # 冗余转速
    
    # 压力冗余计算
    data['pressure_A_redundancy'] = math.sin(t * 0.17) * 10 + 10
    data['pressure_B_redundancy'] = math.sin(t * 0.18) * 10 + 10
    data['pressure_return_redundancy'] = math.sin(t * 0.19) * 5 + 5
    data['pressure_pump1A_redundancy'] = math.sin(t * 0.20) * 15 + 15
    data['pressure_pump1B_redundancy'] = math.sin(t * 0.21) * 15 + 15
    data['pressure_pump2A_redundancy'] = math.sin(t * 0.22) * 15 + 15
    data['pressure_pump2B_redundancy'] = math.sin(t * 0.23) * 15 + 15
    
    return data

def print_simulated_data(data):
    """打印模拟的数据"""
    print("\n===== 模拟接收端数据 =====")
    for key, value in data.items():
        print(f"{key}: {value}")
    print("========================\n")

class SenderGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯发送端")
        self.master.geometry("600x400")
        
        # 创建发送端实例
        self.sender = Protocol422Sender()
        
        # 状态变量
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建GUI组件
        self.create_widgets()
        
        # 获取可用串口列表
        self.update_port_list()
    
    def update_port_list(self):
        """更新可用串口列表"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        if ports:
            self.port_combobox['values'] = ports
            self.port_combobox.current(0)  # 选择第一个串口
        else:
            self.port_combobox['values'] = ["无可用串口"]
            self.port_combobox.current(0)
            self.status_var.set("未检测到可用串口")
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建控制框架
        control_frame = ttk.Frame(main_frame, padding="5")
        control_frame.pack(fill=tk.X, pady=5)
        
        # 串口设置
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT, padx=5)
        self.port_combobox = ttk.Combobox(control_frame, width=10)
        self.port_combobox.pack(side=tk.LEFT, padx=5)
        
        # 刷新串口按钮
        self.refresh_button = ttk.Button(control_frame, text="刷新", command=self.update_port_list)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        # 启动/停止按钮
        self.start_button = ttk.Button(control_frame, text="启动发送", command=self.start_sender)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(control_frame, text="停止发送", command=self.stop_sender)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        self.stop_button.config(state=tk.DISABLED)
        
        # 状态标签
        ttk.Label(control_frame, text="状态:").pack(side=tk.LEFT, padx=(20, 5))
        ttk.Label(control_frame, textvariable=self.status_var).pack(side=tk.LEFT)
        
        # 创建信息框架
        info_frame = ttk.LabelFrame(main_frame, text="发送数据信息", padding="5")
        info_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建文本框显示发送的数据
        self.info_text = tk.Text(info_frame, wrap=tk.WORD, height=10)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(self.info_text, orient=tk.VERTICAL, command=self.info_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.config(yscrollcommand=scrollbar.set)
        
        # 虚拟串口信息框架
        vcom_frame = ttk.LabelFrame(main_frame, text="虚拟串口信息", padding="5")
        vcom_frame.pack(fill=tk.X, pady=5)
        
        # 虚拟串口说明
        vcom_info = (
            "请使用虚拟串口软件创建一对虚拟串口（如COM1-COM2）。\n"
            "发送端使用一个串口（如COM1），接收端使用另一个串口（如COM2）。\n"
            "推荐软件：VSPD (Virtual Serial Port Driver) 或 com0com。"
        )
        ttk.Label(vcom_frame, text=vcom_info, wraplength=550).pack(padx=5, pady=5)
    
    def start_sender(self):
        """启动发送器"""
        port = self.port_combobox.get()
        if port == "无可用串口":
            self.status_var.set("错误: 无可用串口")
            messagebox.showerror("错误", "未检测到可用串口")
            return
        
        # 设置串口
        self.sender.port = port
        
        # 启动发送
        success, message = self.sender.start_sending()
        if success:
            self.status_var.set(message)
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            
            # 启动信息更新线程
            self.update_info_thread = threading.Thread(target=self._update_info_loop)
            self.update_info_thread.daemon = True
            self.update_info_thread.start()
        else:
            self.status_var.set(message)
            messagebox.showerror("启动错误", message)
    
    def stop_sender(self):
        """停止发送器"""
        success, message = self.sender.stop_sending()
        self.status_var.set(message)
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
    
    def _update_info_loop(self):
        """更新信息循环"""
        while self.sender.running:
            # 获取最新一帧数据
            frame, message = self.sender.send_frame()
            
            # 更新文本框
            self.info_text.insert(tk.END, message + "\n")
            self.info_text.see(tk.END)  # 滚动到最新内容
            
            # 限制文本框内容长度
            if float(self.info_text.index(tk.END)) > 100:  # 如果超过100行
                self.info_text.delete(1.0, 2.0)  # 删除第一行
            
            # 等待一段时间
            time.sleep(0.1)  # 更新频率不需要太高
    
    def on_closing(self):
        """窗口关闭时的处理"""
        if hasattr(self, 'sender'):
            self.sender.stop_sending()
            self.sender.disconnect()
        self.master.destroy()

def main():
    root = tk.Tk()
    app = SenderGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
